import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api', // 基础URL
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
})

// loading实例
let loadingInstance = null
// loading计数器，用于处理并发请求
let loadingCount = 0

// 显示loading
const showLoading = () => {
  if (loadingCount === 0) {
    loadingInstance = ElLoading.service({
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
  }
  loadingCount++
}

// 隐藏loading
const hideLoading = () => {
  loadingCount--
  if (loadingCount <= 0) {
    loadingCount = 0
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
  }
}

// 获取token
const getToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token')
}

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 显示loading
    if (config.showLoading === true) {
      showLoading()
    }

    // 添加token
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    hideLoading()
    console.error('请求错误:', error)
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    hideLoading()

    const { data, config } = response
    console.log('data----', data)
    // 这里可以根据后端接口规范调整
    if (data.code === 0 || data.success === true) {
      console.log(data.data)
      return data.data
    }
    if (config.noCodeCheck) {
      return data
    }

    // 业务错误处理
    if (config.showError == true) {
      ElMessage.error(data.msg || '请求失败')
    }

    return Promise.reject(data)
  },
  (error) => {
    hideLoading()

    const { response, config } = error
    let message = '网络错误'

    if (response) {
      switch (response.status) {
        case 401:
          message = '身份验证失败，请重新登录'
          // 清除token
          localStorage.removeItem('token')
          sessionStorage.removeItem('token')
          // 可以在这里跳转到登录页
          router.push('/login')
          break
        case 403:
          message = '没有权限访问该资源'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务暂不可用'
          break
        case 504:
          message = '网关超时'
          break
        default:
          message = response.data?.message || `请求失败 (${response.status})`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    }

    // 显示错误提示
    if (config?.showError == true) {
      ElMessage.error(message)
    }

    return Promise.reject(error)
  },
)

// 通用请求方法
const request = (config) => {
  return service(config)
}

// 过滤无效参数的工具函数
const filterParams = (params) => {
  if (!params || typeof params !== 'object') return {}

  return Object.fromEntries(
    Object.entries(params).filter(
      ([, value]) => value !== undefined && value !== null && value !== '',
    ),
  )
}

// GET请求
const get = (url, params = {}, config = {}) => {
  return service({
    method: 'GET',
    url,
    params: filterParams(params),
    ...config,
  })
}

// POST请求
const post = (url, data = {}, config = {}) => {
  return service({
    method: 'POST',
    url,
    data,
    ...config,
  })
}

// PUT请求
const put = (url, data = {}, config = {}) => {
  return service({
    method: 'PUT',
    url,
    data,
    ...config,
  })
}

// DELETE请求
const del = (url, config = {}) => {
  return service({
    method: 'DELETE',
    url,
    ...config,
  })
}

// PATCH请求
const patch = (url, data = {}, config = {}) => {
  return service({
    method: 'PATCH',
    url,
    data,
    ...config,
  })
}

// 文件上传
const upload = (url, formData, config = {}) => {
  return service({
    method: 'POST',
    url,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    ...config,
  })
}

export default service
export { request, get, post, put, del, patch, upload }
