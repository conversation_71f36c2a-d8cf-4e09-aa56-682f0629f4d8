// 房间状态映射
export const ROOM_STATUS_MAP = {
  CHECKED_IN: { value: 'CHECKED_IN', text: '已入住', color: '#e74c3c' },
  RESERVED: { value: 'RESERVED', text: '已预定', color: '#3498db' },
  AVAILABLE: { value: 'AVAILABLE', text: '可用', color: '#2ecc71' },
  UNAVAILABLE_MAINTENANCE: {
    value: 'UNAVAILABLE_MAINTENANCE',
    text: '不可用（维修中）',
    color: '#95a5a6',
  },
  UNAVAILABLE_CLEANING: {
    value: 'UNAVAILABLE_CLEANING',
    text: '不可用（待清洁）',
    color: '#f39c12',
  },
  UNAVAILABLE_SWITCH_ROOM: {
    value: 'UNAVAILABLE_SWITCH_ROOM',
    text: '不可用（换房审批中）',
    color: '#9b59b6',
  },
  UNAVAILABLE_OTHER: { value: 'UNAVAILABLE_OTHER', text: '不可用（其他原因）', color: '#34495e' },
}

// 房间状态选项
export const ROOM_STATUS_OPTIONS = Object.values(ROOM_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 获取房间状态信息的工具函数
export const getRoomStatusInfo = (status) => {
  return ROOM_STATUS_MAP[status] || { value: 'unknown', text: '未知状态', color: '#bdc3c7' }
}

// 获取房间状态颜色的工具函数
export const getRoomStatusColor = (status) => {
  return ROOM_STATUS_MAP[status]?.color || '#bdc3c7'
}

// 状态标签类型映射（用于Element Plus标签颜色）
export const STATUS_TAG_TYPE_MAP = {
  available: 'success',
  occupied: 'warning',
  maintenance: 'danger',
  cleaning: 'info',
}

// 获取状态标签类型的工具函数
export const getStatusTagType = (status) => {
  return STATUS_TAG_TYPE_MAP[status] || 'info'
}

// 房间设施清单选项
export const FACILITY_OPTIONS = [
  '婴儿床',
  '新生儿推车',
  '恒温垫',
  '温奶器',
  '床头柜',
  '餐桌',
  '凳子',
  '踩脚凳',
  '哺乳凳',
  '沙发',
  '折叠沙发',
  '冰箱',
  '微波炉',
  '烧水壶',
  '独立卫浴',
  '电视',
  'WiFi',
  '空调',
  '暖气',
  '热水器',
  '阳台',
]

// 性别映射
export const GENDER_MAP = {
  1: { value: 1, text: '男' },
  2: { value: 2, text: '女' },
}

// 性别映射（与后端枚举一致）
export const GENDER_CHOICE_MAP = {
  MALE: { value: 'MALE', text: '男' },
  FEMALE: { value: 'FEMALE', text: '女' },
}

// 血型映射
export const BLOOD_TYPE_MAP = {
  A: { value: 'A', text: 'A型' },
  B: { value: 'B', text: 'B型' },
  AB: { value: 'AB', text: 'AB型' },
  O: { value: 'O', text: 'O型' },
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
}

// 紧急联系人关系映射
export const EMERGENCY_CONTACT_RELATION_MAP = {
  FATHER: { value: 'FATHER', text: '父亲' },
  MOTHER: { value: 'MOTHER', text: '母亲' },
  SPOUSE: { value: 'SPOUSE', text: '配偶' },
  CHILDREN: { value: 'CHILDREN', text: '孩子' },
  RELATIVE: { value: 'RELATIVE', text: '亲戚' },
  FRIEND: { value: 'FRIEND', text: '朋友' },
  OTHER: { value: 'OTHER', text: '其他' },
}

// 分娩方式映射
export const DELIVERY_METHOD_MAP = {
  VAGINAL: { value: 'VAGINAL', text: '阴道分娩' },
  CESAREAN: { value: 'CESAREAN', text: '剖宫产' },
  FORCEPS: { value: 'FORCEPS', text: '产钳助产' },
  UNBORN: { value: 'UNBORN', text: '未分娩' },
}

// 入住来源映射
export const CHECK_IN_SOURCE_MAP = {
  IN_HOSPITAL: { value: 'IN_HOSPITAL', text: '院内转入' },
  OUT_HOSPITAL: { value: 'OUT_HOSPITAL', text: '一联体转入' },
  OTHER: { value: 'OTHER', text: '其他' },
}

// 入住状态映射
export const CHECK_IN_STATUS_MAP = {
  RESERVED: { value: 'RESERVED', text: '已预定', tagType: 'warning' },
  CHECKED_IN: { value: 'CHECKED_IN', text: '已入住', tagType: 'success' },
  CHECKED_OUT: { value: 'CHECKED_OUT', text: '已退房', tagType: 'info' },
  CANCELLED: { value: 'CANCELLED', text: '已取消', tagType: 'danger' },
}

// 支付方式映射
export const PAYMENT_METHOD_MAP = {
  CASH: { value: 'CASH', text: '现金' },
  BANK_CARD: { value: 'BANK_CARD', text: '银行卡' },
  CREDIT_CARD: { value: 'CREDIT_CARD', text: '信用卡' },
  WECHAT_PAY: { value: 'WECHAT_PAY', text: '微信支付' },
  ALIPAY_PAY: { value: 'ALIPAY_PAY', text: '支付宝支付' },
  OTHER: { value: 'OTHER', text: '其他' },
}

// 性别选项
export const GENDER_OPTIONS = Object.values(GENDER_MAP).map((gender) => ({
  label: gender.text,
  value: gender.value,
}))

// 紧急联系人关系选项
export const EMERGENCY_CONTACT_RELATION_OPTIONS = Object.values(EMERGENCY_CONTACT_RELATION_MAP).map((relation) => ({
  label: relation.text,
  value: relation.value,
}))

// 工具函数：获取性别文本
export const getGenderText = (value) => {
  return GENDER_MAP[value]?.text || '未知'
}

// 工具函数：获取性别选择文本
export const getGenderChoiceText = (value) => {
  return GENDER_CHOICE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取血型文本
export const getBloodTypeText = (value) => {
  return BLOOD_TYPE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取紧急联系人关系文本
export const getEmergencyContactRelationText = (value) => {
  return EMERGENCY_CONTACT_RELATION_MAP[value]?.text || value || '未知'
}

// 工具函数：获取分娩方式文本
export const getDeliveryMethodText = (value) => {
  return DELIVERY_METHOD_MAP[value]?.text || value || '未知'
}

// 工具函数：获取入住来源文本
export const getCheckInSourceText = (value) => {
  return CHECK_IN_SOURCE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取入住状态文本
export const getCheckInStatusText = (value) => {
  return CHECK_IN_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取支付方式文本
export const getPaymentMethodText = (value) => {
  return PAYMENT_METHOD_MAP[value]?.text || value || '未知'
}

// 入住状态标签类型映射
export const CHECK_IN_STATUS_TAG_TYPE_MAP = {
  RESERVED: 'warning',
  CHECKED_IN: 'success',
  CHECKED_OUT: 'info',
}

// 工具函数：获取入住状态标签类型
export const getCheckInStatusTagType = (status) => {
  return CHECK_IN_STATUS_TAG_TYPE_MAP[status] || 'info'
}

export const CHECK_IN_STATUS_OPTIONS = Object.values(CHECK_IN_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 房间状态图例显示顺序（用于StatusLegend组件）
export const ROOM_STATUS_LEGEND_ORDER = [
  'AVAILABLE',
  'CHECKED_IN',
  'RESERVED',
  'UNAVAILABLE_CLEANING',
  'UNAVAILABLE_MAINTENANCE',
  'UNAVAILABLE_SWITCH_ROOM',
  'UNAVAILABLE_OTHER',
]

// 换房申请状态映射
export const TRANSFER_STATUS_MAP = {
  PENDING: { value: 'PENDING', text: '待处理', tagType: 'warning' },
  APPROVED: { value: 'APPROVED', text: '已批准', tagType: 'success' },
  REJECTED: { value: 'REJECTED', text: '已拒绝', tagType: 'danger' },
  COMPLETED: { value: 'COMPLETED', text: '已完成', tagType: 'info' },
}

// 换房申请状态选项
export const TRANSFER_STATUS_OPTIONS = Object.values(TRANSFER_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取换房状态文本
export const getTransferStatusText = (value) => {
  return TRANSFER_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取换房状态标签类型
export const getTransferStatusTagType = (value) => {
  return TRANSFER_STATUS_MAP[value]?.tagType || 'info'
}

// 外出申请状态映射
export const OUTING_STATUS_MAP = {
  PENDING: { value: 'PENDING', text: '待审批', tagType: 'warning' },
  APPROVED: { value: 'APPROVED', text: '已批准', tagType: 'success' },
  REJECTED: { value: 'REJECTED', text: '已拒绝', tagType: 'danger' },
  OUT: { value: 'OUT', text: '外出中', tagType: 'info' },
  RETURNED: { value: 'RETURNED', text: '已返回', tagType: 'success' },
  OVERDUE: { value: 'OVERDUE', text: '逾期未返', tagType: 'danger' },
}

// 外出申请状态选项
export const OUTING_STATUS_OPTIONS = Object.values(OUTING_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取外出状态文本
export const getOutingStatusText = (value) => {
  return OUTING_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取外出状态标签类型
export const getOutingStatusTagType = (value) => {
  return OUTING_STATUS_MAP[value]?.tagType || 'info'
}

// 外出申请审批状态映射
export const AUDIT_STATUS_MAP = {
  PENDING: { value: 'PENDING', text: '待审批', tagType: 'warning' },
  APPROVED: { value: 'APPROVED', text: '已批准', tagType: 'success' },
  REJECTED: { value: 'REJECTED', text: '已拒绝', tagType: 'danger' },
}

// 外出申请当前状态映射
export const OUTING_CURRENT_STATUS_MAP = {
  NOT_OUT: { value: 'NOT_OUT', text: '未外出', tagType: 'info' },
  OUT: { value: 'OUT', text: '外出中', tagType: 'warning' },
  RETURNED: { value: 'RETURNED', text: '已返回', tagType: 'success' },
  OVERDUE: { value: 'OVERDUE', text: '逾期未返', tagType: 'danger' },
}

// 工具函数：获取审批状态标签类型
export const getAuditStatusTagType = (value) => {
  return AUDIT_STATUS_MAP[value]?.tagType || 'info'
}

// 工具函数：获取当前外出状态标签类型
export const getOutingCurrentStatusTagType = (value) => {
  return OUTING_CURRENT_STATUS_MAP[value]?.tagType || 'info'
}

// 膳食类型映射
export const MEAL_TYPE_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  BREAKFAST: { value: 'BREAKFAST', text: '早餐' },
  EARLY_SNACK: { value: 'EARLY_SNACK', text: '早加餐' },
  LUNCH: { value: 'LUNCH', text: '午餐' },
  NOON_SNACK: { value: 'NOON_SNACK', text: '午加餐' },
  DINNER: { value: 'DINNER', text: '晚餐' },
  LATE_SNACK: { value: 'LATE_SNACK', text: '晚加餐' },
}

// 膳食类型选项（用于表单）
export const MEAL_TYPE_OPTIONS = Object.values(MEAL_TYPE_MAP)
  .filter((meal) => meal.value !== 'UNKNOWN')
  .map((meal) => ({
    label: meal.text,
    value: meal.value,
  }))

// 工具函数：获取膳食类型文本
export const getMealTypeText = (value) => {
  return MEAL_TYPE_MAP[value]?.text || value || '未知'
}

// 活动状态映射
export const ACTIVITY_STATUS_MAP = {
  UPCOMING: { value: 'UPCOMING', text: '即将开始', tagType: 'warning' },
  IN_PROGRESS: { value: 'IN_PROGRESS', text: '进行中', tagType: 'success' },
  COMPLETED: { value: 'COMPLETED', text: '已结束', tagType: 'info' },
}

// 活动状态选项
export const ACTIVITY_STATUS_OPTIONS = Object.values(ACTIVITY_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取活动状态文本
export const getActivityStatusText = (value) => {
  return ACTIVITY_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取活动状态标签类型
export const getActivityStatusTagType = (value) => {
  return ACTIVITY_STATUS_MAP[value]?.tagType || 'info'
}

// 产妇入住评估相关枚举映射

// 面色映射
export const FACE_COLOR_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  RED: { value: 'RED', text: '红润' },
  WHITE: { value: 'WHITE', text: '苍白' },
  YELLOW: { value: 'YELLOW', text: '黄染' },
}

// 口腔黏膜映射
export const ORAL_MUCOSA_MAP = {
  INTACT: { value: 'INTACT', text: '完整' },
  ABNORMAL: { value: 'ABNORMAL', text: '异常' },
}

// 活动能力映射
export const ACTIVITY_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  LIMITED: { value: 'LIMITED', text: '受限' },
}

// 情绪映射
export const EMOTION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  STABLE: { value: 'STABLE', text: '稳定' },
  EXCITED: { value: 'EXCITED', text: '兴奋' },
  DEPRESSED: { value: 'DEPRESSED', text: '郁闷' },
  ANXIETY: { value: 'ANXIETY', text: '焦虑' },
  FEAR: { value: 'FEAR', text: '恐惧' },
}

// 食欲映射
export const APPETITE_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  POOR: { value: 'POOR', text: '不佳' },
  ANTIAE: { value: 'ANTIAE', text: '厌食' },
}

// 饮食要求映射
export const DIETARY_REQUIREMENTS_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  ORDINARY: { value: 'ORDINARY', text: '普食' },
  OTHER: { value: 'OTHER', text: '其他' },
}

// 排尿映射
export const URINATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  FREQUENT: { value: 'FREQUENT', text: '尿频' },
}

// 排便映射
export const DEFECATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  CONSTIPATION: { value: 'CONSTIPATION', text: '便秘' },
  DIARRHEA: { value: 'DIARRHEA', text: '腹泻' },
}

// 康复知识了解映射
export const KNOWLEDGE_OF_REHABILITATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  FAMILIAR: { value: 'FAMILIAR', text: '熟悉' },
  KNOWLEDGE: { value: 'KNOWLEDGE', text: '了解' },
  UNKNOWLEDGE: { value: 'UNKNOWLEDGE', text: '不了解' },
}

// 参与孕期教育映射
export const PARTICIPATION_IN_PREGNANCY_EDUCATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  TOGETHER: { value: 'TOGETHER', text: '共同参与' },
  PERSONAL: { value: 'PERSONAL', text: '本人' },
  FAMILY: { value: 'FAMILY', text: '家属' },
}

// 家属对产妇态度映射
export const FAMILY_ATTITUDE_TO_PATIENT_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  APPROPRIATE_CARE: { value: 'APPROPRIATE_CARE', text: '恰当的关怀' },
  NOT_CARE: { value: 'NOT_CARE', text: '不关心' },
  TOO_CARE: { value: 'TOO_CARE', text: '过于关心' },
  OTHER: { value: 'OTHER', text: '其他' },
}

// 切口位置映射
export const INCISION_POSITION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  ABDOMINAL_WALL: { value: 'ABDOMINAL_WALL', text: '腹壁' },
  PERINEAL_INCISION: { value: 'PERINEAL_INCISION', text: '会阴侧切' },
  PERINEAL_MIDDLE: { value: 'PERINEAL_MIDDLE', text: '会阴正中' },
}

// 切口情况映射
export const INCISION_SITUATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  ABNORMAL: { value: 'ABNORMAL', text: '异常' },
}

// 切口异常映射
export const INCISION_ABNORMAL_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  RED: { value: 'RED', text: '红' },
  SWOLLEN: { value: 'SWOLLEN', text: '肿' },
  PAIN: { value: 'PAIN', text: '痛' },
  SECRETION: { value: 'SECRETION', text: '渗出' },
}

// 乳房情况映射
export const BREAST_SITUATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  FUL: { value: 'FUL', text: '充盈' },
  SWOLLEN: { value: 'SWOLLEN', text: '肿胀' },
  SECONDARY_BREASTS: { value: 'SECONDARY_BREASTS', text: '副乳' },
}

// 乳头情况映射
export const NIPPLE_SITUATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  ABNORMAL: { value: 'ABNORMAL', text: '异常' },
}

// 乳头异常映射
export const NIPPLE_ABNORMAL_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  INVAGINATED: { value: 'INVAGINATED', text: '内陷' },
  CRACKED: { value: 'CRACKED', text: '皲裂' },
  BLISTER: { value: 'BLISTER', text: '水泡' },
  DEFORMITY: { value: 'DEFORMITY', text: '畸形' },
}

// 乳汁情况映射
export const MILK_SITUATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  MORE: { value: 'MORE', text: '多' },
  MIDDLE: { value: 'MIDDLE', text: '中' },
  LESS: { value: 'LESS', text: '少' },
  NONE: { value: 'NONE', text: '无' },
}

// 工具函数：获取面色文本
export const getFaceColorText = (value) => {
  return FACE_COLOR_MAP[value]?.text || value || '未知'
}

// 工具函数：获取口腔黏膜文本
export const getOralMucosaText = (value) => {
  return ORAL_MUCOSA_MAP[value]?.text || value || '未知'
}

// 工具函数：获取活动能力文本
export const getActivityText = (value) => {
  return ACTIVITY_MAP[value]?.text || value || '未知'
}

// 工具函数：获取情绪文本
export const getEmotionText = (emotions) => {
  if (!emotions || !Array.isArray(emotions)) return '未知'
  return emotions.map((emotion) => EMOTION_MAP[emotion]?.text || emotion).join(', ')
}

// 工具函数：获取食欲文本
export const getAppetiteText = (value) => {
  return APPETITE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取饮食要求文本
export const getDietaryRequirementsText = (value) => {
  return DIETARY_REQUIREMENTS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取排尿文本
export const getUrinationText = (value) => {
  return URINATION_MAP[value]?.text || value || '未知'
}

// 工具函数：获取排便文本
export const getDefecationText = (value) => {
  return DEFECATION_MAP[value]?.text || value || '未知'
}

// 工具函数：获取康复知识了解文本
export const getKnowledgeOfRehabilitationText = (value) => {
  return KNOWLEDGE_OF_REHABILITATION_MAP[value]?.text || value || '未知'
}

// 工具函数：获取参与孕期教育文本
export const getParticipationInPregnancyEducationText = (value) => {
  return PARTICIPATION_IN_PREGNANCY_EDUCATION_MAP[value]?.text || value || '未知'
}

// 工具函数：获取家属态度文本
export const getFamilyAttitudeToPatientText = (value) => {
  return FAMILY_ATTITUDE_TO_PATIENT_MAP[value]?.text || value || '未知'
}

// 工具函数：获取切口位置文本
export const getIncisionPositionText = (value) => {
  return INCISION_POSITION_MAP[value]?.text || value || '未知'
}

// 工具函数：获取切口情况文本
export const getIncisionSituationText = (value) => {
  return INCISION_SITUATION_MAP[value]?.text || value || '未知'
}

// 工具函数：获取切口异常文本
export const getIncisionAbnormalText = (value) => {
  if (!value) return '未知'

  // 如果是数组，映射每个项目并用逗号连接
  if (Array.isArray(value)) {
    return value.map((item) => INCISION_ABNORMAL_MAP[item]?.text || item).join(', ')
  }

  // 如果是字符串，直接映射
  if (typeof value === 'string') {
    return INCISION_ABNORMAL_MAP[value]?.text || value
  }

  return '未知'
}

// 工具函数：获取乳房情况文本
export const getBreastSituationText = (situationList) => {
  if (!situationList || !Array.isArray(situationList)) return '未知'
  return situationList.map((item) => BREAST_SITUATION_MAP[item]?.text || item).join(', ')
}

// 工具函数：获取乳头情况文本
export const getNippleSituationText = (value) => {
  return NIPPLE_SITUATION_MAP[value]?.text || value || '未知'
}

// 工具函数：获取乳头异常文本
export const getNippleAbnormalText = (abnormalList) => {
  if (!abnormalList || !Array.isArray(abnormalList)) return '未知'
  return abnormalList.map((item) => NIPPLE_ABNORMAL_MAP[item]?.text || item).join(', ')
}

// 工具函数：获取乳汁情况文本
export const getMilkSituationText = (value) => {
  return MILK_SITUATION_MAP[value]?.text || value || '未知'
}

// 新生儿相关枚举映射

// 喂养方式映射
export const FEEDING_METHOD_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  BREAST_FEEDING: { value: 'BREAST_FEEDING', text: '纯母乳喂养' },
  ARTIFICIAL_FEEDING: { value: 'ARTIFICIAL_FEEDING', text: '人工喂养' },
  MIXED_FEEDING: { value: 'MIXED_FEEDING', text: '混合喂养' },
}

// 新生儿排尿情况映射
export const NEWBORN_URINATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  LOW: { value: 'LOW', text: '尿少' },
}

// 新生儿排便情况映射
export const NEWBORN_BOWEL_MOVEMENT_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  CONSTIPATION: { value: 'CONSTIPATION', text: '便秘' },
  DIARRHEA: { value: 'DIARRHEA', text: '腹泻' },
  NO_MOVEMENT: { value: 'NO_MOVEMENT', text: '未排' },
}

// 面色映射
export const COMPLEXION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  RUDDY: { value: 'RUDDY', text: '红润' },
  PALE: { value: 'PALE', text: '苍白' },
  YELLOW: { value: 'YELLOW', text: '黄染' },
  NORMAL: { value: 'NORMAL', text: '正常' },
}

// 哭声映射
export const CRY_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  LOUD: { value: 'LOUD', text: '响' },
  WEAK: { value: 'WEAK', text: '弱' },
}

// 反应映射
export const REACTION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  GOOD: { value: 'GOOD', text: '良好' },
  POOR: { value: 'POOR', text: '差' },
}

// 四肢映射
export const EXTREMITIES_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  WARM: { value: 'WARM', text: '温暖' },
  COLD: { value: 'COLD', text: '湿冷' },
  STRIPES: { value: 'STRIPES', text: '花斑纹' },
}

// 四肢张力及活动映射
export const EXTREMITY_TONE_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  GOOD: { value: 'GOOD', text: '良好' },
  LIMITED: { value: 'LIMITED', text: '受限' },
}

// 产伤类型映射
export const BIRTH_INJURY_TYPE_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  SKIN_LESIONS: { value: 'SKIN_LESIONS', text: '皮损部位' },
  HEAD_HUMOR: { value: 'HEAD_HUMOR', text: '头血肿' },
}

// 神经反射映射
export const REFLEXES_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  SUCKING: { value: 'SUCKING', text: '吸吮' },
  SWALLOWING: { value: 'SWALLOWING', text: '吞咽' },
  ROOTING: { value: 'ROOTING', text: '觅食' },
  GRASPING: { value: 'GRASPING', text: '握持' },
  MORO: { value: 'MORO', text: '拥抱反射' },
}

// 营养发育映射
export const NUTRITION_DEVELOPMENT_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  EXCESS: { value: 'EXCESS', text: '过剩' },
  DELAYED: { value: 'DELAYED', text: '滞后' },
}

// 皮肤状况映射
export const SKIN_STATUS_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  ABNORMAL: { value: 'ABNORMAL', text: '异常' },
}

// 皮肤异常映射
export const SKIN_ABNORMALITY_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  JAUNDICE: { value: 'JAUNDICE', text: '黄染' },
  INTACT: { value: 'INTACT', text: '完整' },
  LESION: { value: 'LESION', text: '皮损' },
  ERYTHEMA: { value: 'ERYTHEMA', text: '红斑' },
  RASH: { value: 'RASH', text: '皮疹' },
  PUSTULE: { value: 'PUSTULE', text: '脓包' },
  INDURATION: { value: 'INDURATION', text: '硬肿' },
  EDEMA: { value: 'EDEMA', text: '水肿' },
  OTHER: { value: 'OTHER', text: '其他' },
}

// 前囟映射
export const ANTERIOR_FONTANELLE_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  FLAT: { value: 'FLAT', text: '平坦' },
  BULGING: { value: 'BULGING', text: '肿胀' },
  INTACT: { value: 'INTACT', text: '凹陷' },
}

// 口腔黏膜异常映射
export const ORAL_MUCOSA_ABNORMALITY_MAP = {
  BREAKDOWN: { value: 'BREAKDOWN', text: '破溃' },
  CANDIDA: { value: 'CANDIDA', text: '鹅口疮' },
}

// 畸形类型映射
export const ANOMALY_TYPE_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  LIP_PALATE: { value: 'LIP_PALATE', text: '唇腭裂' },
  NOSE: { value: 'NOSE', text: '鼻' },
  EAR: { value: 'EAR', text: '耳' },
  EXTREMITY: { value: 'EXTREMITY', text: '四肢' },
  VAGINA: { value: 'VAGINA', text: '外阴' },
  URETHRA_HYPOSPADIAS: { value: 'URETHRA_HYPOSPADIAS', text: '尿道下裂' },
  ANAL_ATRESIA: { value: 'ANAL_ATRESIA', text: '肛门闭锁' },
  OTHER: { value: 'OTHER', text: '其他' },
}

// 脐部映射
export const UMBILICAL_CORD_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  DRY: { value: 'DRY', text: '干燥' },
  ABNORMAL: { value: 'ABNORMAL', text: '异常' },
}

// 脐部异常映射
export const UMBILICAL_CORD_ABNORMALITY_MAP = {
  RED_AND_SWOLLEN: { value: 'RED_AND_SWOLLEN', text: '红肿' },
  SEEPAGE: { value: 'SEEPAGE', text: '渗液' },
  HEMORRHAGE: { value: 'HEMORRHAGE', text: '渗血' },
}

// 吸吮情况映射
export const SUCKING_ABILITY_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  GOOD: { value: 'GOOD', text: '好' },
  AVERAGE: { value: 'AVERAGE', text: '一般' },
  POOR: { value: 'POOR', text: '差' },
}

// 臀部映射
export const BUTTOCKS_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  ABNORMAL: { value: 'ABNORMAL', text: '异常' },
}

// 臀部异常映射
export const BUTTOCKS_ABNORMALITY_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  SLIGHTLY_RED: { value: 'SLIGHTLY_RED', text: '稍红' },
  RED_BUTTOCKS: { value: 'RED_BUTTOCKS', text: '红臀' },
  LESION: { value: 'LESION', text: '皮损' },
  SCAB: { value: 'SCAB', text: '结痂' },
}

// 疫苗接种映射
export const VACCINE_INJECTION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  HEPATITIS_B_VACCINE_FIRST_SHOT: {
    value: 'HEPATITIS_B_VACCINE_FIRST_SHOT',
    text: '乙肝疫苗第一针',
  },
  BCG: { value: 'BCG', text: '卡介苗' },
  OTHER: { value: 'OTHER', text: '其他' },
}

// 部位映射
export const LOCATION_MAP = {
  FRONTAL: { value: 'FRONTAL', text: '前额' },
  FRONT_CHEST: { value: 'FRONT_CHEST', text: '前胸' },
  THIGH: { value: 'THIGH', text: '大腿' },
  LOWER_LEG_OUTSIDE: { value: 'LOWER_LEG_OUTSIDE', text: '小腿外侧' },
}

// 工具函数：获取喂养方式文本
export const getFeedingMethodText = (value) => {
  return FEEDING_METHOD_MAP[value]?.text || value || '未知'
}

// 工具函数：获取新生儿排尿情况文本
export const getNewbornUrinationText = (value) => {
  return NEWBORN_URINATION_MAP[value]?.text || value || '未知'
}

// 工具函数：获取新生儿排便情况文本
export const getNewbornBowelMovementText = (value) => {
  return NEWBORN_BOWEL_MOVEMENT_MAP[value]?.text || value || '未知'
}

// 工具函数：获取面色文本
export const getComplexionText = (value) => {
  return COMPLEXION_MAP[value]?.text || value || '未知'
}

// 访客状态映射
export const VISITOR_STATUS_MAP = {
  PENDING: { value: 'PENDING', text: '待访问', tagType: 'warning' },
  VISITING: { value: 'VISITING', text: '正在访问', tagType: 'success' },
  LEAVED: { value: 'LEAVED', text: '已离开', tagType: 'info' },
}

// 访客状态选项
export const VISITOR_STATUS_OPTIONS = Object.values(VISITOR_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取访客状态文本
export const getVisitorStatusText = (value) => {
  return VISITOR_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取访客状态标签类型
export const getVisitorStatusTagType = (value) => {
  return VISITOR_STATUS_MAP[value]?.tagType || 'info'
}


// 访客预约状态映射
export const VISITOR_APPOINTMENT_STATUS_MAP = {
  PENDING: { value: 'PENDING', text: '待审批', tagType: 'warning' },
  CONFIRMED: { value: 'CONFIRMED', text: '已审批', tagType: 'success' },
  REJECTED: { value: 'REJECTED', text: '已拒绝', tagType: 'danger' },
  CANCELLED: { value: 'CANCELLED', text: '已取消', tagType: 'info' },
  COMPLETED: { value: 'COMPLETED', text: '已完成', tagType: 'success' },
}

// 工具函数：获取访客预约状态文本
export const getVisitorAppointmentStatusText = (value) => {
  return VISITOR_APPOINTMENT_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取访客预约状态标签类型

export const getVisitorAppointmentStatusTagType = (value) => {
  return VISITOR_APPOINTMENT_STATUS_MAP[value]?.tagType || 'info'
}

// 工具函数：获取哭声文本
export const getCryText = (value) => {
  return CRY_MAP[value]?.text || value || '未知'
}

// 工具函数：获取反应文本
export const getReactionText = (value) => {
  return REACTION_MAP[value]?.text || value || '未知'
}

// 工具函数：获取四肢文本
export const getExtremitiesText = (value) => {
  return EXTREMITIES_MAP[value]?.text || value || '未知'
}

// 工具函数：获取四肢张力文本
export const getExtremityToneText = (value) => {
  return EXTREMITY_TONE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取产伤类型文本
export const getBirthInjuryTypeText = (value) => {
  return BIRTH_INJURY_TYPE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取神经反射文本
export const getReflexesText = (value) => {
  return REFLEXES_MAP[value]?.text || value || '未知'
}

// 工具函数：获取营养发育文本
export const getNutritionDevelopmentText = (value) => {
  return NUTRITION_DEVELOPMENT_MAP[value]?.text || value || '未知'
}

// 工具函数：获取皮肤状况文本
export const getSkinStatusText = (value) => {
  return SKIN_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取皮肤异常文本
export const getSkinAbnormalityText = (value) => {
  return SKIN_ABNORMALITY_MAP[value]?.text || value || '未知'
}

// 工具函数：获取前囟文本
export const getAnteriorFontanelleText = (value) => {
  return ANTERIOR_FONTANELLE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取口腔黏膜异常文本
export const getOralMucosaAbnormalityText = (value) => {
  return ORAL_MUCOSA_ABNORMALITY_MAP[value]?.text || value || '未知'
}

// 工具函数：获取畸形类型文本
export const getAnomalyTypeText = (value) => {
  return ANOMALY_TYPE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取脐部文本
export const getUmbilicalCordText = (value) => {
  return UMBILICAL_CORD_MAP[value]?.text || value || '未知'
}

// 工具函数：获取脐部异常文本
export const getUmbilicalCordAbnormalityText = (value) => {
  return UMBILICAL_CORD_ABNORMALITY_MAP[value]?.text || value || '未知'
}

// 工具函数：获取吸吮情况文本
export const getSuckingAbilityText = (value) => {
  return SUCKING_ABILITY_MAP[value]?.text || value || '未知'
}

// 工具函数：获取臀部文本
export const getButtocksText = (value) => {
  return BUTTOCKS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取臀部异常文本
export const getButtocksAbnormalityText = (value) => {
  return BUTTOCKS_ABNORMALITY_MAP[value]?.text || value || '未知'
}

// 工具函数：获取疫苗接种文本
export const getVaccineInjectionText = (value) => {
  return VACCINE_INJECTION_MAP[value]?.text || value || '未知'
}

// 工具函数：获取部位文本
export const getLocationText = (value) => {
  return LOCATION_MAP[value]?.text || value || '未知'
}

// 选项数组（用于表单组件）
export const BLOOD_TYPE_OPTIONS = Object.values(BLOOD_TYPE_MAP).map((item) => ({
  label: item.text,
  value: item.value,
}))

export const LOCATION_OPTIONS = Object.values(LOCATION_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const FEEDING_METHOD_OPTIONS = Object.values(FEEDING_METHOD_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const SUCKING_ABILITY_OPTIONS = Object.values(SUCKING_ABILITY_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const NEWBORN_URINATION_OPTIONS = Object.values(NEWBORN_URINATION_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const NEWBORN_BOWEL_MOVEMENT_OPTIONS = Object.values(NEWBORN_BOWEL_MOVEMENT_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const BUTTOCKS_OPTIONS = Object.values(BUTTOCKS_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const BUTTOCKS_ABNORMALITY_OPTIONS = Object.values(BUTTOCKS_ABNORMALITY_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const COMPLEXION_OPTIONS = Object.values(COMPLEXION_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const CRY_OPTIONS = Object.values(CRY_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const REACTION_OPTIONS = Object.values(REACTION_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const EXTREMITIES_OPTIONS = Object.values(EXTREMITIES_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const EXTREMITY_TONE_OPTIONS = Object.values(EXTREMITY_TONE_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const BIRTH_INJURY_TYPE_OPTIONS = Object.values(BIRTH_INJURY_TYPE_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const REFLEXES_OPTIONS = Object.values(REFLEXES_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const NUTRITION_DEVELOPMENT_OPTIONS = Object.values(NUTRITION_DEVELOPMENT_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const SKIN_STATUS_OPTIONS = Object.values(SKIN_STATUS_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const SKIN_ABNORMALITY_OPTIONS = Object.values(SKIN_ABNORMALITY_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const ANTERIOR_FONTANELLE_OPTIONS = Object.values(ANTERIOR_FONTANELLE_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const ORAL_MUCOSA_OPTIONS = Object.values(ORAL_MUCOSA_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const ORAL_MUCOSA_ABNORMALITY_OPTIONS = Object.values(ORAL_MUCOSA_ABNORMALITY_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const ANOMALY_TYPE_OPTIONS = Object.values(ANOMALY_TYPE_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const UMBILICAL_CORD_OPTIONS = Object.values(UMBILICAL_CORD_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const UMBILICAL_CORD_ABNORMALITY_OPTIONS = Object.values(UMBILICAL_CORD_ABNORMALITY_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

export const VACCINE_INJECTION_OPTIONS = Object.values(VACCINE_INJECTION_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

// 是否选择映射
export const YES_NO_MAP = {
  YES: { value: 'YES', text: '是' },
  NO: { value: 'NO', text: '否' },
}

// 工具函数：获取是否选择文本
export const getYesNoText = (value) => {
  return YES_NO_MAP[value]?.text || value || '未知'
}

// 排尿状况映射（详细版本）
export const URINATION_STATUS_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  FREQUENT: { value: 'FREQUENT', text: '尿频' },
  PAINFUL: { value: 'PAINFUL', text: '尿痛' },
}

// 工具函数：获取排尿状况文本
export const getUrinationStatusText = (value) => {
  return URINATION_STATUS_MAP[value]?.text || value || '未知'
}

// 伤口状况映射
export const WOUND_STATUS_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  REDNESS: { value: 'REDNESS', text: '红肿' },
  SWELLING: { value: 'SWELLING', text: '肿胀' },
  PAIN: { value: 'PAIN', text: '疼痛' },
  DISCHARGE: { value: 'DISCHARGE', text: '渗液' },
  BLEEDING: { value: 'BLEEDING', text: '渗血' },
}

// 工具函数：获取伤口状况文本
export const getWoundStatusText = (value) => {
  return WOUND_STATUS_MAP[value]?.text || value || '未知'
}

// 满月营养发育映射
export const MONTH_NUTRITION_DEVELOPMENT_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  GOOD: { value: 'GOOD', text: '良好' },
  EXCESS: { value: 'EXCESS', text: '过剩' },
  AVERAGE: { value: 'AVERAGE', text: '中等' },
  POOR: { value: 'POOR', text: '差' },
}

// 工具函数：获取满月营养发育文本
export const getMonthNutritionDevelopmentText = (value) => {
  return MONTH_NUTRITION_DEVELOPMENT_MAP[value]?.text || value || '未知'
}

// 满月面色映射
export const MONTH_SKIN_COLOR_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  RED: { value: 'RED', text: '红润' },
  YELLOW: { value: 'YELLOW', text: '黄染' },
}

// 工具函数：获取满月面色文本
export const getMonthSkinColorText = (value) => {
  return MONTH_SKIN_COLOR_MAP[value]?.text || value || '未知'
}

// 满月哭声映射
export const MONTH_CRY_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  LOUD: { value: 'LOUD', text: '响亮' },
  WEAK: { value: 'WEAK', text: '微弱' },
}

// 工具函数：获取满月哭声文本
export const getMonthCryText = (value) => {
  return MONTH_CRY_MAP[value]?.text || value || '未知'
}

// 满月反应映射
export const MONTH_REACTION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  GOOD: { value: 'GOOD', text: '良好' },
  DULL: { value: 'DULL', text: '迟钝' },
  POOR: { value: 'POOR', text: '差' },
}

// 工具函数：获取满月反应文本
export const getMonthReactionText = (value) => {
  return MONTH_REACTION_MAP[value]?.text || value || '未知'
}

// 满月皮肤状况映射
export const MONTH_SKIN_STATUS_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  INTACT: { value: 'INTACT', text: '完整' },
  DAMAGED: { value: 'DAMAGED', text: '破损' },
  RED: { value: 'RED', text: '红润' },
  YELLOW: { value: 'YELLOW', text: '黄染' },
  RED_BUTTOCKS: { value: 'RED_BUTTOCKS', text: '红臀' },
  RASH: { value: 'RASH', text: '皮疹' },
}

// 工具函数：获取满月皮肤状况文本
export const getMonthSkinStatusText = (value) => {
  if (Array.isArray(value)) {
    if (value.length === 0) return '未知'
    return value.map(v => MONTH_SKIN_STATUS_MAP[v]?.text || v).join('、')
  }
  return MONTH_SKIN_STATUS_MAP[value]?.text || value || '未知'
}

// 满月喂养情况映射
export const MONTH_FEEDING_SITUATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  BREASTFEEDING: { value: 'BREASTFEEDING', text: '母乳喂养' },
  ARTIFICIAL_FEEDING: { value: 'ARTIFICIAL_FEEDING', text: '人工喂养' },
  MIXED_FEEDING: { value: 'MIXED_FEEDING', text: '混合喂养' },
}

// 工具函数：获取满月喂养情况文本
export const getMonthFeedingSituationText = (value) => {
  return MONTH_FEEDING_SITUATION_MAP[value]?.text || value || '未知'
}

// 满月喂养指导映射
export const MONTH_FEEDING_GUIDANCE_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  BREAST: { value: 'BREAST', text: '母乳' },
  FORMULA: { value: 'FORMULA', text: '奶粉' },
  MIXED: { value: 'MIXED', text: '混合喂养' },
  ADD_SUPPLEMENT: { value: 'ADD_SUPPLEMENT', text: '按时添加辅食' },
  FEW_TIMES_MORE_FOOD: { value: 'FEW_TIMES_MORE_FOOD', text: '少食多餐' },
  OTHER: { value: 'OTHER', text: '其他' },
}

// 工具函数：获取满月喂养指导文本
export const getMonthFeedingGuidanceText = (value) => {
  return MONTH_FEEDING_GUIDANCE_MAP[value]?.text || value || '未知'
}

// 满月预防感冒映射
export const MONTH_PREVENT_COLD_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  WARM: { value: 'WARM', text: '注意保暖' },
  REDUCE_VISIT: { value: 'REDUCE_VISIT', text: '减少探视' },
  SUNLIGHT_BATH: { value: 'SUNLIGHT_BATH', text: '日光浴' },
  FEW_PUBLIC_PLACES: { value: 'FEW_PUBLIC_PLACES', text: '少到公共场合' },
  IF_UNWELL_SEEK_MEDICAL_ATTENDANCE: {
    value: 'IF_UNWELL_SEEK_MEDICAL_ATTENDANCE',
    text: '如有不适随时到医院就诊',
  },
}

// 工具函数：获取满月预防感冒文本
export const getMonthPreventColdText = (value) => {
  return MONTH_PREVENT_COLD_MAP[value]?.text || value || '未知'
}

// 肌张力及活动映射
export const MUSCLE_TONE_ACTIVITY_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  GOOD: { value: 'GOOD', text: '良好' },
  LIMITED: { value: 'LIMITED', text: '受限' },
}

// 工具函数：获取肌张力及活动文本
export const getMuscleToneActivityText = (value) => {
  return MUSCLE_TONE_ACTIVITY_MAP[value]?.text || value || '未知'
}

// 设备类型映射
export const EQUIPMENT_TYPE_MAP = {
  MEDICAL: { value: 'MEDICAL', text: '医疗设备', tagType: 'danger' },
  NURING: { value: 'NURING', text: '护理设备', tagType: 'success' },
  CLEAN: { value: 'CLEAN', text: '清洁设备', tagType: 'warning' },
  KITCHEN: { value: 'KITCHEN', text: '厨房设备', tagType: 'primary' },
  OTHER: { value: 'OTHER', text: '其他设备', tagType: 'info' },
}

// 设备类型选项
export const EQUIPMENT_TYPE_OPTIONS = Object.values(EQUIPMENT_TYPE_MAP).map((type) => ({
  label: type.text,
  value: type.value,
}))

// 设备状态映射
export const EQUIPMENT_STATUS_MAP = {
  USING: { value: 'USING', text: '使用中', tagType: 'success' },
  MAINTAINING: { value: 'MAINTAINING', text: '维护中', tagType: 'warning' },
  DISABLED: { value: 'DISABLED', text: '停用', tagType: 'danger' },
}

// 设备状态选项
export const EQUIPMENT_STATUS_OPTIONS = Object.values(EQUIPMENT_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取设备类型文本
export const getEquipmentTypeText = (value) => {
  return EQUIPMENT_TYPE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取设备类型标签类型
export const getEquipmentTypeTagType = (value) => {
  return EQUIPMENT_TYPE_MAP[value]?.tagType || 'info'
}

// 工具函数：获取设备状态文本
export const getEquipmentStatusText = (value) => {
  return EQUIPMENT_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取设备状态标签类型
export const getEquipmentStatusTagType = (value) => {
  return EQUIPMENT_STATUS_MAP[value]?.tagType || 'info'
}

// 医疗转诊申请状态映射（与后端ReferralStatusEnum保持一致）
export const MEDICAL_REFERRAL_STATUS_MAP = {
  PENDING_AUDIT: { value: 'PENDING_AUDIT', text: '待审核', tagType: 'warning' },
  PENDING_REFERRAL: { value: 'PENDING_REFERRAL', text: '待转诊', tagType: 'info' },
  REJECTED: { value: 'REJECTED', text: '已拒绝', tagType: 'danger' },
  REFERRALING: { value: 'REFERRALING', text: '转诊中', tagType: 'primary' },
  REFERRALLED: { value: 'REFERRALLED', text: '已转诊', tagType: 'success' },
  RETURNED: { value: 'RETURNED', text: '已返回', tagType: 'success' },
}

// 医疗转诊申请状态选项
export const MEDICAL_REFERRAL_STATUS_OPTIONS = Object.values(MEDICAL_REFERRAL_STATUS_MAP).map(
  (status) => ({
    label: status.text,
    value: status.value,
  }),
)

// 工具函数：获取医疗转诊状态文本
export const getMedicalReferralStatusText = (value) => {
  return MEDICAL_REFERRAL_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取医疗转诊状态标签类型
export const getMedicalReferralStatusTagType = (value) => {
  return MEDICAL_REFERRAL_STATUS_MAP[value]?.tagType || 'info'
}

// 紧急程度映射（与后端EmergencyLevelEnum保持一致）
export const EMERGENCY_LEVEL_MAP = {
  GENERAL: { value: 'GENERAL', text: '一般', tagType: 'info' },
  EMERGENCY: { value: 'EMERGENCY', text: '紧急', tagType: 'warning' },
  VERY_EMERGENCY: { value: 'VERY_EMERGENCY', text: '非常紧急', tagType: 'danger' },
}

// 紧急程度选项
export const EMERGENCY_LEVEL_OPTIONS = Object.values(EMERGENCY_LEVEL_MAP).map((level) => ({
  label: level.text,
  value: level.value,
}))

// 工具函数：获取紧急程度文本
export const getEmergencyLevelText = (value) => {
  return EMERGENCY_LEVEL_MAP[value]?.text || value || '未知'
}

// 工具函数：获取紧急程度标签类型
export const getEmergencyLevelTagType = (value) => {
  return EMERGENCY_LEVEL_MAP[value]?.tagType || 'info'
}

// 满月评估相关选项数组（用于表单组件）
export const MONTH_NUTRITION_DEVELOPMENT_OPTIONS = Object.values(MONTH_NUTRITION_DEVELOPMENT_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({
    label: item.text,
    value: item.value,
  }))

export const MONTH_SKIN_COLOR_OPTIONS = Object.values(MONTH_SKIN_COLOR_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({
    label: item.text,
    value: item.value,
  }))

export const MONTH_CRY_OPTIONS = Object.values(MONTH_CRY_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({
    label: item.text,
    value: item.value,
  }))

export const MONTH_REACTION_OPTIONS = Object.values(MONTH_REACTION_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({
    label: item.text,
    value: item.value,
  }))

export const MONTH_SKIN_STATUS_OPTIONS = Object.values(MONTH_SKIN_STATUS_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({
    label: item.text,
    value: item.value,
  }))

export const MONTH_FEEDING_SITUATION_OPTIONS = Object.values(MONTH_FEEDING_SITUATION_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({
    label: item.text,
    value: item.value,
  }))

export const MONTH_FEEDING_GUIDANCE_OPTIONS = Object.values(MONTH_FEEDING_GUIDANCE_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({
    label: item.text,
    value: item.value,
  }))

export const MONTH_PREVENT_COLD_OPTIONS = Object.values(MONTH_PREVENT_COLD_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({
    label: item.text,
    value: item.value,
  }))

export const MUSCLE_TONE_ACTIVITY_OPTIONS = Object.values(MUSCLE_TONE_ACTIVITY_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({
    label: item.text,
    value: item.value,
  }))

// 康复切口情况映射
export const REHABILITATION_INCISION_SITUATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  RED: { value: 'RED', text: '红肿' },
  SECRETION: { value: 'SECRETION', text: '渗液' },
}

// 工具函数：获取康复切口情况文本
export const getRehabilitationIncisionSituationText = (value) => {
  return REHABILITATION_INCISION_SITUATION_MAP[value]?.text || value || '未知'
}

// 运动后评估映射
export const EXERCISE_AFTER_EVALUATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  GOOD: { value: 'GOOD', text: '较好' },
  AVERAGE: { value: 'AVERAGE', text: '一般' },
  POOR: { value: 'POOR', text: '较差' },
}

// 工具函数：获取运动后评估文本
export const getExerciseAfterEvaluationText = (value) => {
  return EXERCISE_AFTER_EVALUATION_MAP[value]?.text || value || '未知'
}

// 运动后评估选项
export const EXERCISE_AFTER_EVALUATION_OPTIONS = Object.values(EXERCISE_AFTER_EVALUATION_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({
    label: item.text,
    value: item.value,
  }))

// 进食情况映射
export const EATING_SITUATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  FINISHED: { value: 'FINISHED', text: '吃完' },
  REMAINING_LITTLE: { value: 'REMAINING_LITTLE', text: '剩余少量' },
  REMAINING_MUCH: { value: 'REMAINING_MUCH', text: '剩余多量' },
  NOT_EATEN: { value: 'NOT_EATEN', text: '未吃' },
}

// 工具函数：获取进食情况文本
export const getEatingSituationText = (value) => {
  return EATING_SITUATION_MAP[value]?.text || value || '未知'
}

// 特殊饮食特点映射
export const SPECIAL_DIET_FEATURES_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  DIABETES_DIET: { value: 'DIABETES_DIET', text: '糖尿病饮食' },
  LOW_SALT_DIET: { value: 'LOW_SALT_DIET', text: '低盐饮食' },
  HIGH_PROTEIN_DIET: { value: 'HIGH_PROTEIN_DIET', text: '高蛋白饮食' },
}

// 工具函数：获取特殊饮食特点文本
export const getSpecialDietFeaturesText = (value) => {
  return SPECIAL_DIET_FEATURES_MAP[value]?.text || value || '未知'
}

// 产妇康复护理评估相关映射

// 康复切口情况映射（与后端enum保持一致）
export const RECOVERY_INCISION_SITUATION_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  NORMAL: { value: 'NORMAL', text: '正常' },
  RED: { value: 'RED', text: '红肿' },
  SECRETION: { value: 'SECRETION', text: '渗液' },
}

// 工具函数：获取康复切口情况文本
export const getRecoveryIncisionSituationText = (value) => {
  if (Array.isArray(value)) {
    if (value.length === 0) return '未知'
    return value.map(v => RECOVERY_INCISION_SITUATION_MAP[v]?.text || v).join('、')
  }
  return RECOVERY_INCISION_SITUATION_MAP[value]?.text || value || '未知'
}

// 选项数组（用于表单组件）
export const RECOVERY_INCISION_SITUATION_OPTIONS = Object.values(
  RECOVERY_INCISION_SITUATION_MAP,
).map((item) => ({
  label: item.text,
  value: item.value,
}))

// 喂养方式枚举映射（与后端FeedingMethodChoice保持一致）
export const FEEDING_METHOD_CHOICE_MAP = {
  UNKNOWN: { value: 'UNKNOWN', text: '未知' },
  BREAST_FEEDING: { value: 'BREAST_FEEDING', text: '母乳喂养' },
  ARTIFICIAL_FEEDING: { value: 'ARTIFICIAL_FEEDING', text: '人工喂养' },
  MIXED_FEEDING: { value: 'MIXED_FEEDING', text: '混合喂养' },
}

// 喂养方式选项（用于表单组件）
export const FEEDING_METHOD_CHOICE_OPTIONS = Object.values(FEEDING_METHOD_CHOICE_MAP)
  .map((item) => ({
    label: item.text,
    value: item.value,
  }))

// 工具函数：获取喂养方式文本
export const getFeedingMethodChoiceText = (value) => {
  return FEEDING_METHOD_CHOICE_MAP[value]?.text || value || '未知'
}

// 上报状态映射（与后端ReportStatusEnum保持一致）
export const REPORT_STATUS_MAP = {
  NO_NEED_REPORT: { value: 'NO_NEED_REPORT', text: '无需上报', tagType: 'info' },
  ALREADY_REPORT: { value: 'ALREADY_REPORT', text: '已上报', tagType: 'success' },
  NOT_REPORT: { value: 'NOT_REPORT', text: '未上报', tagType: 'warning' },
}

// 上报状态选项
export const REPORT_STATUS_OPTIONS = Object.values(REPORT_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取上报状态文本
export const getReportStatusText = (value) => {
  return REPORT_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取上报状态标签类型
export const getReportStatusTagType = (value) => {
  return REPORT_STATUS_MAP[value]?.tagType || 'info'
}

// 消毒规范文档状态映射
export const DISINFECTION_DOCUMENT_STATUS_MAP = {
  DRAFT: { value: 'DRAFT', text: '草稿', tagType: 'info' },
  IN_EFFECT: { value: 'IN_EFFECT', text: '生效中', tagType: 'success' },
  DISABLED: { value: 'DISABLED', text: '已停用', tagType: 'danger' },
}

// 消毒规范文档状态选项
export const DISINFECTION_DOCUMENT_STATUS_OPTIONS = Object.values(DISINFECTION_DOCUMENT_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取消毒规范文档状态文本
export const getDisinfectionDocumentStatusText = (value) => {
  return DISINFECTION_DOCUMENT_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取消毒规范文档状态标签类型
export const getDisinfectionDocumentStatusTagType = (value) => {
  return DISINFECTION_DOCUMENT_STATUS_MAP[value]?.tagType || 'info'
}

// 消毒执行结果映射
export const CLEAN_DISINFECTION_RESULT_MAP = {
  NOT_EXECUTED: { value: 'NOT_EXECUTED', text: '未执行', tagType: 'info' },
  PENDING: { value: 'PENDING', text: '等待中', tagType: 'warning' },
  PASS: { value: 'PASS', text: '合格', tagType: 'success' },
  FAIL: { value: 'FAIL', text: '不合格', tagType: 'danger' },
}

// 消毒执行结果选项
export const CLEAN_DISINFECTION_RESULT_OPTIONS = Object.values(CLEAN_DISINFECTION_RESULT_MAP).map((result) => ({
  label: result.text,
  value: result.value,
}))

// 工具函数：获取消毒执行结果文本
export const getCleanDisinfectionResultText = (value) => {
  return CLEAN_DISINFECTION_RESULT_MAP[value]?.text || value || '未知'
}

// 工具函数：获取消毒执行结果标签类型
export const getCleanDisinfectionResultTagType = (value) => {
  return CLEAN_DISINFECTION_RESULT_MAP[value]?.tagType || 'info'
}

// 班次类型映射
export const SHIFT_TYPE_MAP = {
  MORNING: { value: 'MORNING', text: '早班', tagType: 'success', color: 'text-green-500', time: '08:00-12:00', bgColor: 'bg-green-100' },
  DAY_SHIFT: { value: 'DAY_SHIFT', text: '早班', tagType: 'success', color: 'text-green-500', time: '08:00-12:00', bgColor: 'bg-green-100' },
  AFTERNOON: { value: 'AFTERNOON', text: '中班', tagType: 'warning', color: 'text-orange-500', time: '12:00-18:00', bgColor: 'bg-orange-100' },
  NIGHT: { value: 'NIGHT', text: '晚班', tagType: 'info', color: 'text-blue-500', time: '18:00-08:00', bgColor: 'bg-blue-100' },
  NIGHT_SHIFT: { value: 'NIGHT_SHIFT', text: '夜班', tagType: 'primary', color: 'text-purple-500', time: '20:00-08:00', bgColor: 'bg-purple-100' },
}

// 班次类型选项
export const SHIFT_TYPE_OPTIONS = Object.values(SHIFT_TYPE_MAP).filter((type) => ['MORNING', 'AFTERNOON', 'NIGHT'].includes(type.value)).map((type) => ({
  label: type.text,
  value: type.value,
}))

// 工具函数：获取班次类型文本
export const getShiftTypeText = (value) => {
  return SHIFT_TYPE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取班次类型标签类型
export const getShiftTypeTagType = (value) => {
  return SHIFT_TYPE_MAP[value]?.tagType || 'info'
}

// 排班状态映射
export const SCHEDULE_STATUS_MAP = {
  PENDING: { value: 'PENDING', text: '待排班', tagType: 'warning' },
  SCHEDULED: { value: 'SCHEDULED', text: '已排班', tagType: 'success' },
  COMPLETED: { value: 'COMPLETED', text: '已完成', tagType: 'info' },
}

// 排班状态选项
export const SCHEDULE_STATUS_OPTIONS = Object.values(SCHEDULE_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取排班状态文本
export const getScheduleStatusText = (value) => {
  return SCHEDULE_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取排班状态标签类型
export const getScheduleStatusTagType = (value) => {
  return SCHEDULE_STATUS_MAP[value]?.tagType || 'info'
}

// 排班明细状态映射
export const SCHEDULE_DETAIL_STATUS_MAP = {
  PENDING: { value: 'PENDING', text: '待确认', tagType: 'warning' },
  CONFIRMED: { value: 'CONFIRMED', text: '已确认', tagType: 'success' },
  REJECTED: { value: 'REJECTED', text: '已拒绝', tagType: 'danger' },
}

// 排班明细状态选项
export const SCHEDULE_DETAIL_STATUS_OPTIONS = Object.values(SCHEDULE_DETAIL_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取排班明细状态文本
export const getScheduleDetailStatusText = (value) => {
  return SCHEDULE_DETAIL_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取排班明细状态标签类型
export const getScheduleDetailStatusTagType = (value) => {
  return SCHEDULE_DETAIL_STATUS_MAP[value]?.tagType || 'info'
}

// 交接班状态映射
export const HANDOVER_STATUS_MAP = {
  DRAFT: { value: 'DRAFT', text: '草稿', tagType: 'info' },
  PENDING: { value: 'PENDING', text: '待确认', tagType: 'warning' },
  CONFIRMED: { value: 'CONFIRMED', text: '已确认', tagType: 'success' },
  REJECTED: { value: 'REJECTED', text: '已拒绝', tagType: 'danger' },
}

// 交接班状态选项
export const HANDOVER_STATUS_OPTIONS = Object.values(HANDOVER_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取交接班状态文本
export const getHandoverStatusText = (value) => {
  return HANDOVER_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取交接班状态标签类型
export const getHandoverStatusTagType = (value) => {
  return HANDOVER_STATUS_MAP[value]?.tagType || 'info'
}

// 发布状态映射
export const PUBLISH_STATUS_MAP = {
  UNPUBLISHED: { value: 'UNPUBLISHED', text: '未发布', tagType: 'info' },
  PUBLISHED: { value: 'PUBLISHED', text: '已发布', tagType: 'success' },
  EXPIRED: { value: 'EXPIRED', text: '已过期', tagType: 'warning' },
}

// 发布状态选项
export const PUBLISH_STATUS_OPTIONS = Object.values(PUBLISH_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取发布状态文本
export const getPublishStatusText = (value) => {
  return PUBLISH_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取发布状态标签类型
export const getPublishStatusTagType = (value) => {
  return PUBLISH_STATUS_MAP[value]?.tagType || 'info'
}

// 机构排班状态映射（与后端OrgScheduleStatusEnum保持一致）
export const ORG_SCHEDULE_STATUS_MAP = {
  PENDING: { value: 'PENDING', text: '待上岗', tagType: 'warning' },
  ON_DUTY: { value: 'ON_DUTY', text: '在岗中', tagType: 'success' },
  OFF_DUTY: { value: 'OFF_DUTY', text: '已下岗', tagType: 'info' },
}

// 机构排班状态选项
export const ORG_SCHEDULE_STATUS_OPTIONS = Object.values(ORG_SCHEDULE_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取机构排班状态文本
export const getOrgScheduleStatusText = (value) => {
  return ORG_SCHEDULE_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取机构排班状态标签类型
export const getOrgScheduleStatusTagType = (value) => {
  return ORG_SCHEDULE_STATUS_MAP[value]?.tagType || 'info'
}

// 个人排班状态映射（与后端ScheduleStatusEnum保持一致）
export const PERSONAL_SCHEDULE_STATUS_MAP = {
  PENDING: { value: 'PENDING', text: '待接班', tagType: 'warning' },
  ACCEPTED: { value: 'ACCEPTED', text: '已接班', tagType: 'success' },
  FINISHED: { value: 'FINISHED', text: '已下班', tagType: 'info' },
}

// 个人排班状态选项
export const PERSONAL_SCHEDULE_STATUS_OPTIONS = Object.values(PERSONAL_SCHEDULE_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取个人排班状态文本
export const getPersonalScheduleStatusText = (value) => {
  return PERSONAL_SCHEDULE_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取个人排班状态标签类型
export const getPersonalScheduleStatusTagType = (value) => {
  return PERSONAL_SCHEDULE_STATUS_MAP[value]?.tagType || 'info'
}

// 培训结果映射（与后端TrainingResultEnum保持一致）
export const TRAINING_RESULT_MAP = {
  PASS: { value: 'PASS', text: '合格', tagType: 'success' },
  FAIL: { value: 'FAIL', text: '不合格', tagType: 'danger' },
  ABSENT: { value: 'ABSENT', text: '缺勤', tagType: 'warning' },
  PENDING: { value: 'PENDING', text: '待评估', tagType: 'info' },
}

// 培训结果选项
export const TRAINING_RESULT_OPTIONS = Object.values(TRAINING_RESULT_MAP).map((result) => ({
  label: result.text,
  value: result.value,
}))

// 工具函数：获取培训结果文本
export const getTrainingResultText = (value) => {
  return TRAINING_RESULT_MAP[value]?.text || value || '未知'
}

// 工具函数：获取培训结果标签类型
export const getTrainingResultTagType = (value) => {
  return TRAINING_RESULT_MAP[value]?.tagType || 'info'
}

// 反馈类型映射（与后端FeedbackTypeEnum保持一致）
export const FEEDBACK_TYPE_MAP = {
  SUGGESTION: { value: 'SUGGESTION', text: '意见建议', tagType: 'info' },
  SERVICE: { value: 'SERVICE', text: '服务反馈', tagType: 'warning' },
  ENVIRONMENT: { value: 'ENVIRONMENT', text: '环境反馈', tagType: 'primary' },
  FACILITY: { value: 'FACILITY', text: '设施反馈', tagType: 'success' },
  OTHER: { value: 'OTHER', text: '其他反馈', tagType: 'info' },
}

// 反馈类型选项
export const FEEDBACK_TYPE_OPTIONS = Object.values(FEEDBACK_TYPE_MAP).map((type) => ({
  label: type.text,
  value: type.value,
}))

// 工具函数：获取反馈类型文本
export const getFeedbackTypeText = (value) => {
  return FEEDBACK_TYPE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取反馈类型标签类型
export const getFeedbackTypeTagType = (value) => {
  return FEEDBACK_TYPE_MAP[value]?.tagType || 'info'
}

// 反馈状态映射（与后端FeedbackStatusEnum保持一致）
export const FEEDBACK_STATUS_MAP = {
  PENDING: { value: 'PENDING', text: '待处理', tagType: 'warning' },
  PROCESSING: { value: 'PROCESSING', text: '处理中', tagType: 'primary' },
  RESOLVED: { value: 'RESOLVED', text: '已处理', tagType: 'success' },
}

// 反馈状态选项
export const FEEDBACK_STATUS_OPTIONS = Object.values(FEEDBACK_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取反馈状态文本
export const getFeedbackStatusText = (value) => {
  return FEEDBACK_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取反馈状态标签类型
export const getFeedbackStatusTagType = (value) => {
  return FEEDBACK_STATUS_MAP[value]?.tagType || 'info'
}

// 问卷类型映射（与后端QuestionnaireTypeEnum保持一致）
export const QUESTIONNAIRE_TYPE_MAP = {
  SATISFACTION: { value: 'SATISFACTION', text: '满意度调查', tagType: 'primary' },
  HEALTH_STATUS: { value: 'HEALTH_STATUS', text: '健康状况', tagType: 'success' },
  SERVICE_EVALUATION: { value: 'SERVICE_EVALUATION', text: '服务评价', tagType: 'warning' },
  CUSTOM: { value: 'CUSTOM', text: '自定义', tagType: 'info' },
}

// 问卷类型选项
export const QUESTIONNAIRE_TYPE_OPTIONS = Object.values(QUESTIONNAIRE_TYPE_MAP).map((type) => ({
  label: type.text,
  value: type.value,
}))

// 工具函数：获取问卷类型文本
export const getQuestionnaireTypeText = (value) => {
  return QUESTIONNAIRE_TYPE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取问卷类型标签类型
export const getQuestionnaireTypeTagType = (value) => {
  return QUESTIONNAIRE_TYPE_MAP[value]?.tagType || 'info'
}

// 问题类型映射（与后端QuestionTypeEnum保持一致）
export const QUESTION_TYPE_MAP = {
  SINGLE_CHOICE: { value: 'SINGLE_CHOICE', text: '单选', tagType: 'primary' },
  MULTIPLE_CHOICE: { value: 'MULTIPLE_CHOICE', text: '多选', tagType: 'success' },
  TRUE_FALSE: { value: 'TRUE_FALSE', text: '判断', tagType: 'warning' },
  STAR_RATING: { value: 'STAR_RATING', text: '星级评分', tagType: 'danger' },
  TEXT: { value: 'TEXT', text: '文本', tagType: 'info' },
}



// 问题类型选项
export const QUESTION_TYPE_OPTIONS = Object.values(QUESTION_TYPE_MAP).map((type) => ({
  label: type.text,
  value: type.value,
}))

// 工具函数：获取问题类型文本
export const getQuestionTypeText = (value) => {
  value = value.toUpperCase()
  return QUESTION_TYPE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取问题类型标签类型
export const getQuestionTypeTagType = (value) => {
  value = value.toUpperCase()
  return QUESTION_TYPE_MAP[value]?.tagType || 'info'
}

// 工具函数：判断是否为选择题类型
export const isChoiceQuestion = (questionType) => {
  questionType = questionType.toUpperCase()
  return questionType === 'SINGLE_CHOICE' || questionType === 'MULTIPLE_CHOICE'
}

// 工具函数：判断是否为单选题
export const isSingleChoiceQuestion = (questionType) => {
  questionType = questionType.toUpperCase()
  return questionType === 'SINGLE_CHOICE'
}

// 工具函数：判断是否为多选题
export const isMultipleChoiceQuestion = (questionType) => {
  questionType = questionType.toUpperCase()
  return questionType === 'MULTIPLE_CHOICE'
}

// 工具函数：判断是否为文本题
export const isTextQuestion = (questionType) => {
  questionType = questionType.toUpperCase()
  return questionType === 'TEXT'
}

// 工具函数：判断是否为判断题
export const isTrueFalseQuestion = (questionType) => {
  questionType = questionType.toUpperCase()
  return questionType === 'TRUE_FALSE'
}

// 工具函数：判断是否为星级评分题
export const isStarRatingQuestion = (questionType) => {
  questionType = questionType.toUpperCase()
  return questionType === 'STAR_RATING'
}

// 工具函数：判断问题类型是否需要选项
export const questionNeedsChoices = (questionType) => {
  questionType = questionType.toUpperCase()
  return isChoiceQuestion(questionType)
}

// 工具函数：获取问题类型的选项标签前缀
export const getChoicePrefix = (questionType, index) => {
  questionType = questionType.toUpperCase()
  if (isChoiceQuestion(questionType)) {
    return String.fromCharCode(65 + index) + '.' // A. B. C. D.
  }
  return `${index + 1}.` // 1. 2. 3. 4.
}



// 问卷状态映射（与后端QuestionnaireStatusEnum保持一致）
export const QUESTIONNAIRE_STATUS_MAP = {
  UPCOMING: { value: 'UPCOMING', text: '即将开始', tagType: 'warning' },
  IN_PROGRESS: { value: 'IN_PROGRESS', text: '进行中', tagType: 'success' },
  COMPLETED: { value: 'COMPLETED', text: '已结束', tagType: 'info' },
}

// 问卷状态选项
export const QUESTIONNAIRE_STATUS_OPTIONS = Object.values(QUESTIONNAIRE_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取问卷状态文本
export const getQuestionnaireStatusText = (value) => {
  return QUESTIONNAIRE_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取问卷状态标签类型
export const getQuestionnaireStatusTagType = (value) => {
  return QUESTIONNAIRE_STATUS_MAP[value]?.tagType || 'info'
}

// 产后康复项目状态映射
export const POSTPARTUM_PROJECT_STATUS_MAP = {
  ENABLED: { value: 'ENABLED', text: '启用', tagType: 'success' },
  DISABLED: { value: 'DISABLED', text: '禁用', tagType: 'danger' },
}

// 产后康复项目状态选项
export const POSTPARTUM_PROJECT_STATUS_OPTIONS = Object.values(POSTPARTUM_PROJECT_STATUS_MAP).map((status) => ({
  label: status.text,
  value: status.value,
}))

// 工具函数：获取产后康复项目状态文本
export const getPostpartumProjectStatusText = (value) => {
  return POSTPARTUM_PROJECT_STATUS_MAP[value]?.text || value || '未知'
}

// 工具函数：获取产后康复项目状态标签类型
export const getPostpartumProjectStatusTagType = (value) => {
  return POSTPARTUM_PROJECT_STATUS_MAP[value]?.tagType || 'info'
}

// 问卷可用阶段映射（与后端QuestionnaireAvailableStageEnum保持一致）
export const QUESTIONNAIRE_AVAILABLE_STAGE_MAP = {
  AFTER_AWEEK: { value: 'AFTER_AWEEK', text: '入住一周', tagType: 'primary' },
  MEDIUM_TERM: { value: 'MEDIUM_TERM', text: '中期', tagType: 'warning' },
  BEFORE_DISCHARGE: { value: 'BEFORE_DISCHARGE', text: '出院前', tagType: 'danger' },
  GENERAL: { value: 'GENERAL', text: '通用', tagType: 'info' },
}

// 问卷可用阶段选项
export const QUESTIONNAIRE_AVAILABLE_STAGE_OPTIONS = Object.values(QUESTIONNAIRE_AVAILABLE_STAGE_MAP).map((stage) => ({
  label: stage.text,
  value: stage.value,
}))

// 工具函数：获取问卷可用阶段文本
export const getQuestionnaireAvailableStageText = (value) => {
  return QUESTIONNAIRE_AVAILABLE_STAGE_MAP[value]?.text || value || '未知'
}

// 工具函数：获取问卷可用阶段标签类型
export const getQuestionnaireAvailableStageTagType = (value) => {
  return QUESTIONNAIRE_AVAILABLE_STAGE_MAP[value]?.tagType || 'info'
}
