import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useMenuStore } from '@/stores/menu'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: {
        requiresAuth: false,
        title: '登录',
      },
    },
    {
      path: '/',
      redirect: '/dashboard',
    },
    {
      path: '/',
      component: () => import('../views/LayoutView.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: 'dashboard',
          name: 'dashboard',
          component: () => import('../views/DashboardView.vue'),
          meta: {
            title: '仪表盘',
          },
        },
        // Housekeeping Management
        {
          path: 'housekeeping',
          redirect: '/housekeeping/overview',
          children: [
            {
              path: 'overview',
              name: 'housekeeping-overview',
              component: () => import('../views/housekeeping/OverviewView.vue'),
              meta: {
                title: '房间状态总览',
              },
            },
            {
              path: 'maternal-management',
              name: 'housekeeping-maternal-management',
              component: () => import('../views/maternal-records/MaternalManagementView.vue'),
              meta: {
                title: '产妇管理',
              },
            },
            {
              path: 'checkin',
              name: 'housekeeping-checkin',
              component: () => import('../views/housekeeping/CheckinView.vue'),
              meta: {
                title: '入住管理',
              },
            },
            {
              path: 'checkout',
              name: 'housekeeping-checkout',
              component: () => import('../views/housekeeping/CheckoutView.vue'),
              meta: {
                title: '退房管理',
              },
            },
            {
              path: 'rooms',
              name: 'housekeeping-rooms',
              component: () => import('../views/housekeeping/RoomsView.vue'),
              meta: {
                title: '房间信息管理',
              },
            },
            {
              path: 'transfer',
              name: 'housekeeping-transfer',
              component: () => import('../views/housekeeping/TransferView.vue'),
              meta: {
                title: '换房管理',
              },
            },
            {
              path: 'outing',
              name: 'housekeeping-outing',
              component: () => import('../views/housekeeping/OutingView.vue'),
              meta: {
                title: '外出管理',
              },
            },
          ],
        },
        // Maternal Records
        {
          path: 'maternal-records',
          redirect: '/maternal-records/customers',
          children: [
            {
              path: 'customers',
              name: 'maternal-customers',
              component: () => import('../views/maternal-records/CustomersView.vue'),
              meta: {
                title: '客户列表',
              },
            },
            {
              path: 'maternal-management',
              name: 'maternal-management',
              component: () => import('../views/maternal-records/MaternalManagementView.vue'),
              meta: {
                title: '产妇管理',
              },
            },
            {
              path: 'details/:id?',
              name: 'maternal-details',
              component: () => import('../views/maternal-records/DetailsView.vue'),
              meta: {
                title: '客户详情',
                activeMenu: '/maternal-records/customers',
                breadcrumbs: [
                  { title: '母婴核心记录', path: '/maternal-records' },
                  { title: '客户列表', path: '/maternal-records/customers' },
                  { title: '{name}', dynamic: true },
                ],
              },
            },
          ],
        },
        // Billing Management
        {
          path: 'billing',
          redirect: '/billing/packages',
          children: [
            {
              path: 'packages',
              name: 'billing-packages',
              component: () => import('../views/billing/PackagesView.vue'),
              meta: {
                title: '套餐价格管理',
              },
            },
            {
              path: 'statements',
              name: 'billing-statements',
              component: () => import('../views/billing/StatementsView.vue'),
              meta: {
                title: '账单管理',
              },
            },
            {
              path: 'statistics',
              name: 'billing-statistics',
              component: () => import('../views/billing/StatisticsView.vue'),
              meta: {
                title: '财务统计',
              },
            },
          ],
        },
        // Infection Control & Environment
        {
          path: 'infection-control',
          name: 'infection-control',
          component: () => import('../views/InfectionControlView.vue'),
          meta: {
            title: '感染控制与环境',
          },
        },
        // Internal Referral
        {
          path: 'internal-referral',
          name: 'internal-referral',
          component: () => import('../views/InternalReferralView.vue'),
          meta: {
            title: '院内转诊',
          },
        },
        // Meal Management
        {
          path: 'meal-management',
          redirect: '/meal-management/dishes',
          children: [
            {
              path: 'dishes',
              name: 'dish-management',
              component: () => import('../views/meal-management/DishesView.vue'),
              meta: {
                title: '菜品管理',
              },
            },
            {
              path: 'planning',
              name: 'meal-planning',
              component: () => import('../views/meal-management/PlanningView.vue'),
              meta: {
                title: '菜单规划',
              },
            },
            {
              path: 'planning/patient/:id',
              name: 'patient-meal-detail',
              component: () => import('../views/meal-management/PatientMealDetailView.vue'),
              meta: {
                title: '配餐详情',
                activeMenu: '/meal-management/planning',
                breadcrumbs: [
                  { title: '膳食管理', path: '/meal-management' },
                  { title: '菜单规划', path: '/meal-management/planning' },
                  { title: '{name}', dynamic: true }, // 不设置path，让它自动使用当前路径
                ],
              },
            },
            {
              path: 'settings',
              name: 'meal-settings',
              component: () => import('../views/meal-management/SettingsView.vue'),
              meta: {
                title: '膳食设置',
              },
            },
            {
              path: 'nutrition',
              name: 'nutrition-analysis',
              component: () => import('../views/meal-management/NutritionView.vue'),
              meta: {
                title: '营养分析',
              },
            },
          ],
        },
        // Activity Management
        {
          path: 'activity-management',
          redirect: '/activity-management/list',
          children: [
            {
              path: 'list',
              name: 'activity-list',
              component: () => import('../views/activity-management/ListView.vue'),
              meta: {
                title: '活动列表',
              },
            },
            {
              path: 'publish',
              name: 'activity-publish',
              component: () => import('../views/activity-management/PublishView.vue'),
              meta: {
                title: '发布活动',
              },
            },
            {
              path: 'settings',
              name: 'activity-settings',
              component: () => import('../views/activity-management/SettingsView.vue'),
              meta: {
                title: '活动设置',
              },
            },
          ],
        },
        // Health Education
        {
          path: 'health-education',
          name: 'health-education',
          component: () => import('../views/HealthEducationView.vue'),
          meta: {
            title: '健康教育管理',
          },
        },
        // Staff Scheduling
        {
          path: 'staff-scheduling',
          redirect: '/staff-scheduling/staff',
          children: [
            {
              path: 'staff',
              name: 'staff-info',
              component: () => import('../views/staff-scheduling/StaffView.vue'),
              meta: {
                title: '员工信息管理',
              },
            },
            {
              path: 'scheduling',
              name: 'scheduling',
              component: () => import('../views/staff-scheduling/SchedulingView.vue'),
              meta: {
                title: '排班管理',
              },
            },
          ],
        },
        // Duty Handover
        {
          path: 'duty-handover',
          redirect: '/duty-handover/duty',
          children: [
            {
              path: 'duty',
              name: 'duty-info',
              component: () => import('../views/duty-handover/DutyView.vue'),
              meta: {
                title: '值班信息',
              },
            },
            {
              path: 'handover',
              name: 'handover',
              component: () => import('../views/duty-handover/HandoverView.vue'),
              meta: {
                title: '电子交班',
              },
            },
          ],
        },
        // Customer Feedback
        {
          path: 'customer-feedback',
          name: 'customer-feedback',
          component: () => import('../views/CustomerFeedbackView.vue'),
          meta: {
            title: '客户反馈管理',
          },
        },
        // Equipment Management
        {
          path: 'equipment-management',
          name: 'equipment-management',
          component: () => import('../views/EquipmentManagementView.vue'),
          meta: {
            title: '设备管理',
          },
        },
        // Visitor Management
        {
          path: 'visitor-management',
          name: 'visitor-management',
          component: () => import('../views/VisitorManagementView.vue'),
          meta: {
            title: '访客管理',
          },
        },
        // Visitor Appointment
        {
          path: 'visitor-appointment',
          name: 'visitor-appointment',
          component: () => import('../views/VisitorAppointmentView.vue'),
          meta: {
            title: '预约参观(小程序)',
          },
        },
        // Disinfection Management
        {
          path: 'disinfection-management',
          redirect: '/disinfection-management/records',
          children: [
            {
              path: 'records',
              name: 'disinfection-records',
              component: () => import('../views/disinfection-management/RecordsView.vue'),
              meta: {
                title: '清洁消毒记录',
              },
            },
            {
              path: 'standards',
              name: 'disinfection-standards',
              component: () => import('../views/disinfection-management/StandardsView.vue'),
              meta: {
                title: '消毒规范文库',
              },
            },
          ],
        },
        // Postpartum Rehabilitation
        {
          path: 'postpartum-rehabilitation',
          redirect: '/postpartum-rehabilitation/project-management',
          children: [
            {
              path: 'project-management',
              name: 'postpartum-project-management',
              component: () => import('../views/postpartum-management/ProjectManagementView.vue'),
              meta: {
                title: '产后康复项目管理',
              },
            },
            {
              path: 'records',
              name: 'postpartum-records',
              component: () => import('../views/postpartum-management/RecordsView.vue'),
              meta: {
                title: '产康记录',
              },
            },
          ],
        },
        // Ward Round Management
        {
          path: 'ward-round-management',
          redirect: '/ward-round-management/maternity',
          children: [
            {
              path: 'maternity',
              name: 'maternity-ward-round',
              component: () => import('../views/ward-round-management/MaternityWardRoundView.vue'),
              meta: {
                title: '产妇查房',
              },
            },
            {
              path: 'newborn',
              name: 'newborn-ward-round',
              component: () => import('../views/ward-round-management/NewbornWardRoundView.vue'),
              meta: {
                title: '新生儿查房',
              },
            },
          ],
        },
        // Report Export
        {
          path: 'report-export',
          name: 'report-export',
          component: () => import('../views/ReportExportView.vue'),
          meta: {
            title: '报表导出中心',
          },
        },
        // System Management
        {
          path: 'system-management',
          redirect: '/system-management/users',
          children: [
            {
              path: 'users',
              name: 'user-management',
              component: () => import('../views/system-management/UsersView.vue'),
              meta: {
                title: '用户管理',
              },
            },
            {
              path: 'roles',
              name: 'role-permission',
              component: () => import('../views/system-management/RolesView.vue'),
              meta: {
                title: '角色权限管理',
              },
            },
            {
              path: 'audit',
              name: 'audit-log',
              component: () => import('../views/system-management/AuditView.vue'),
              meta: {
                title: '审计日志',
              },
            },
            {
              path: 'his-config',
              name: 'his-config',
              component: () => import('../views/system-management/HisConfigView.vue'),
              meta: {
                title: 'HIS集成配置',
              },
            },
            {
              path: 'wechat-app',
              name: 'wechat-app',
              component: () => import('../views/system-management/WechatAppView.vue'),
              meta: {
                title: '微信小程序管理',
              },
            },
          ],
        },
      ],
    },
    // 404 页面
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFoundView.vue'),
      meta: {
        requiresAuth: false,
        title: '页面未找到',
      },
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  const menuStore = useMenuStore()

  // 初始化用户信息
  authStore.initUser()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 医院月子中心管理台`
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth !== false && !authStore.isLoggedIn) {
    next('/login')
    return
  }

  // 如果已登录但访问登录页，重定向到首页
  if (to.name === 'login' && authStore.isLoggedIn) {
    next('/dashboard')
    return
  }

  // 检查权限
  if (authStore.isLoggedIn && to.path !== '/dashboard') {
    // 使用新的权限检查方法
    if (!menuStore.canAccessPath(to.path)) {
      // 没有权限，重定向到首页
      next('/dashboard')
      return
    }
  }

  next()
})

export default router
