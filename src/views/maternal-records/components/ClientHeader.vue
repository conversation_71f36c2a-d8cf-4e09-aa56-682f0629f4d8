<template>
  <div class="client-header bg-white rounded-lg p-6 mb-6 shadow-sm">
    <div class="flex justify-between items-start">
      <!-- 客户信息网格 -->
      <div class="client-info grid grid-cols-3 gap-6 flex-1">
        <div class="client-info-item">
          <div class="client-info-label text-sm text-gray-500 mb-2">产妇姓名</div>
          <div class="client-info-value text-lg font-semibold text-gray-900 mb-3">
            {{ clientData.maternity_name }} (ID: {{ clientData.id }})
          </div>
          <div class="client-badges flex gap-2 flex-wrap">
            <span
              v-if="clientData.is_multiple_birth"
              class="badge px-3 py-1 rounded-full text-sm flex items-center gap-2 text-white transition-all hover:scale-105 bg-pink-500 hover:bg-pink-600"
            >
              <User class="w-4 h-4" />
              多胞胎
            </span>
            <span
              v-if="clientData.delivery_method"
              class="badge px-3 py-1 rounded-full text-sm flex items-center gap-2 text-white transition-all hover:scale-105 bg-green-500 hover:bg-green-600"
            >
              <CircleCheck class="w-4 h-4" />
              {{ clientData.delivery_method }}
            </span>
            <span
              v-if="clientData.check_in_status_display"
              :class="[
                'badge px-3 py-1 rounded-full text-sm flex items-center gap-2 text-white transition-all hover:scale-105',
                getStatusBadgeClass(clientData.check_in_status_display),
              ]"
            >
              <Clock class="w-4 h-4" />
              {{ clientData.check_in_status_display }}
            </span>
          </div>
        </div>

        <div class="client-info-item">
          <div class="client-info-label text-sm text-gray-500 mb-2">年龄</div>
          <div class="client-info-value text-lg font-semibold text-gray-900">
            {{ clientData.maternity_age }}岁
          </div>
        </div>

        <div class="client-info-item">
          <div class="client-info-label text-sm text-gray-500 mb-2">房间号</div>
          <div class="client-info-value text-lg font-semibold text-gray-900">
            {{ clientData.room_number === '-' ? '未分配' : clientData.room_number }}
          </div>
        </div>

        <div class="client-info-item">
          <div class="client-info-label text-sm text-gray-500 mb-2">入院日期</div>
          <div class="client-info-value text-lg font-semibold text-gray-900">
            {{ clientData.check_in_date }}
          </div>
        </div>

        <div class="client-info-item">
          <div class="client-info-label text-sm text-gray-500 mb-2">预计出院</div>
          <div class="client-info-value text-lg font-semibold text-gray-900">
            {{ clientData.check_out_date }}
          </div>
        </div>

        <div class="client-info-item">
          <div class="client-info-label text-sm text-gray-500 mb-2">主管护士</div>
          <div class="client-info-value text-lg font-semibold text-gray-900">
            {{ clientData.chief_nurse_name || '未分配' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { User, CircleCheck, Clock } from '@element-plus/icons-vue'

defineProps({
  clientData: {
    type: Object,
    required: true,
  },
})

const getStatusBadgeClass = (status) => {
  const classes = {
    已入住: 'bg-pink-500 hover:bg-pink-600',
    预约中: 'bg-orange-500 hover:bg-orange-600',
    已出院: 'bg-gray-500 hover:bg-gray-600',
  }
  return classes[status] || 'bg-gray-500 hover:bg-gray-600'
}
</script>

<style scoped>
.client-header {
  transition: all 0.3s ease;
}

.client-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.client-info-item {
  background-color: #fdf2f8;
  padding: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.client-info-item:hover {
  background-color: #fce7f3;
  transform: translateY(-1px);
}

.badge {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
