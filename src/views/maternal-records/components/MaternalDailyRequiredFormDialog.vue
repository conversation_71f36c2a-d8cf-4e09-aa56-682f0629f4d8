<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="600px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="记录日期" prop="record_date">
              <el-date-picker
                v-model="form.record_date"
                type="date"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择记录日期"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 生命体征 -->
        <div class="form-section mb-6">
          <h4 class="section-title">生命体征</h4>
          <div class="space-y-4">
            <el-form-item label="血压 (mmHg)" prop="blood_pressure">
              <el-input v-model="form.blood_pressure" placeholder="如：120/80" />
            </el-form-item>
            <el-form-item label="体重 (kg)" prop="weight">
              <el-input-number
                v-model="form.weight"
                :min="30"
                :max="200"
                :precision="1"
                class="w-full"
                placeholder="请输入体重"
              />
            </el-form-item>
            <el-form-item label="体温 (°C)" prop="temperature">
              <el-input-number
                v-model="form.temperature"
                :min="35"
                :max="42"
                :precision="1"
                class="w-full"
                placeholder="请输入体温"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put } from '@/utils/request.js'
import { getCurrentTime } from '@/utils/dateUtils.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: null,
  },
  customerId: {
    type: [String, Number],
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const isEdit = computed(() => !!props.detail)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑产妇每日必填记录' : '新建产妇每日必填记录'
})

const form = reactive({
  record_date: '',
  blood_pressure: '',
  weight: null,
  temperature: null,
})

const rules = {
  record_date: [{ required: true, message: '请选择记录日期', trigger: 'change' }],
  blood_pressure: [{ required: true, message: '请输入血压', trigger: 'blur' }],
  weight: [{ required: true, message: '请输入体重', trigger: 'blur' }],
  temperature: [{ required: true, message: '请输入体温', trigger: 'blur' }],
}

const { scrollToTop } = useDialogScrollToTop()

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach((key) => {
    if (['weight', 'temperature'].includes(key)) {
      form[key] = null
    } else {
      form[key] = ''
    }
  })
  nextTick(() => formRef.value?.clearValidate())
}

// 监听 visible 变化
watch(
  () => props.modelValue,
  async (visible) => {
    scrollToTop()
    if (visible) {
      if (props.detail) {
        // 使用传入的详情数据
        resetForm()
        await nextTick()
        console.log('Detail data received:', props.detail)

        Object.assign(form, props.detail)
        console.log('Form data after assignment:', { ...form })
      } else {
        // 初始化默认值
        resetForm()
        await nextTick()
        form.record_date = getCurrentTime('date')
      }
    }
  },
)

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 处理提交数据
    const submitData = { ...form }

    console.log('Submit data:', submitData)

    let res
    if (!isEdit.value) {
      // 创建记录
      res = await post(`customer-service/mdr-record/create/${props.customerId}/`, submitData)
      ElMessage.success('产妇每日必填记录创建成功！')
    } else {
      // 更新记录
      res = await put(`customer-service/mdr-record/update/${props.detail.record_id}/`, submitData)
      ElMessage.success('产妇每日必填记录更新成功！')
    }

    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
