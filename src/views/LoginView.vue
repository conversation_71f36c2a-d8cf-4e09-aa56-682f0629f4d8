<template>
  <div
    class="min-h-screen bg-gradient-to-br from-pink-50 to-pink-100 flex items-center justify-center px-4"
  >
    <div class="max-w-md w-full">
      <!-- Logo and Title -->
      <div class="text-center mb-8">
        <div
          class="mx-auto h-16 w-16 rounded-full flex items-center justify-center mb-4"
          style="background-color: rgb(231, 127, 161)"
        >
          <House class="w-8 h-8 text-white" />
        </div>
        <h2 class="text-3xl font-bold text-gray-900">医院月子中心管理台</h2>
        <p class="text-gray-600 mt-2">月子中心管理系统</p>
      </div>

      <!-- Login Form -->
      <div class="bg-white rounded-lg shadow-xl p-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6 text-center">登录</h3>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="rules"
          @submit.prevent="handleLogin"
          size="large"
        >
          <el-form-item prop="phone">
            <el-input v-model="loginForm.phone" placeholder="手机号" class="w-full">
              <template #prefix>
                <User class="w-4 h-4 text-gray-400" />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              show-password
              class="w-full"
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <Lock class="w-4 h-4 text-gray-400" />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" class="w-full" :loading="loading" @click="handleLogin">
              登录
            </el-button>
          </el-form-item>
          <el-form-item>
            <div class="flex justify-center w-full">
              <el-link type="primary" underline="never">忘记密码？</el-link>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <div class="text-center mt-6 text-sm text-gray-600">
        © 2025 医院月子中心管理台. 版权所有.
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { User, Lock, House } from '@element-plus/icons-vue'
import { get, post } from '@/utils/request'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const loginFormRef = ref(null)

const loginForm = reactive({
  phone: '',
  password: '',
})

const rules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少需要6个字符', trigger: 'blur' },
  ],
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const result = await authStore.login(loginForm)

        if (result.success) {
          ElMessage.success('登录成功！')
          router.push('/dashboard')
        }
      } catch (error) {
        console.log('loginview error', error)
        ElMessage.error(`登录失败: ${error.message}`)
        // ElMessage.error('登录失败，请重试。')
      } finally {
        loading.value = false
      }
    }
  })
}

// 如果已经登录，直接跳转到dashboard
if (authStore.isLoggedIn) {
  router.push('/dashboard')
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}

code {
  font-size: 12px;
}

/* 去除input自动填充的样式 */
:deep(.el-input__inner:-webkit-autofill) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #606266 !important;
  background-color: white !important;
  background-image: none !important;
}

:deep(.el-input__inner:-webkit-autofill:hover) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #606266 !important;
  background-color: white !important;
}

:deep(.el-input__inner:-webkit-autofill:focus) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #606266 !important;
  background-color: white !important;
}

:deep(.el-input__inner:-webkit-autofill:active) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #606266 !important;
  background-color: white !important;
}
</style>
