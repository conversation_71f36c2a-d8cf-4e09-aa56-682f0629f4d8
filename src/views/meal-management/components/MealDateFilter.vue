<template>
  <div class="meal-date-filter-container bg-white border border-gray-200 rounded-lg p-4">
    <div class="flex items-center justify-center">
      <!-- 日期导航 -->
      <div class="date-navigation flex items-center gap-3">
        <el-button
          @click="previousDate"
          circle
          size="small"
          class="date-nav-btn border-gray-300 hover:border-pink-300 hover:text-pink-500"
        >
          <el-icon><ArrowLeft /></el-icon>
        </el-button>

        <div class="date-selector flex items-center gap-2">
          <el-date-picker
            v-model="currentDate"
            type="date"
            placeholder="选择日期"
            format="YYYY年MM月DD日"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
            :disabled-date="disabledDate"
            class="date-picker"
            :clearable="false"
            size="default"
          />
          <span class="week-day text-gray-600 text-sm w-14">{{ weekDay }}</span>
        </div>

        <el-button
          @click="nextDate"
          circle
          size="small"
          class="date-nav-btn border-gray-300 hover:border-pink-300 hover:text-pink-500"
        >
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElButton, ElDatePicker, ElIcon, ElMessage } from 'element-plus'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { getWeekDay, getNextAvailableDate } from '@/utils/utils.js'
import { format, parseISO, addDays, subDays } from 'date-fns'

// 定义属性
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  disabledDates: {
    type: Array,
    default: () => [],
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const currentDate = ref(props.modelValue || getNextAvailableDate(props.disabledDates))

// 计算属性
const weekDay = computed(() => {
  return getWeekDay(currentDate.value)
})

// 禁用日期判断
const disabledDate = (time) => {
  const dateStr = format(time, 'yyyy-MM-dd')
  return props.disabledDates.includes(dateStr)
}

// 监听日期变化
watch(currentDate, (newDate) => {
  emit('update:modelValue', newDate)
})

// 监听外部 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      currentDate.value = newValue
    }
  },
)

// 监听禁用日期变化，自动调整当前日期
watch(
  () => props.disabledDates,
  (newDisabledDates) => {
    // 如果当前日期在新的禁用列表中，自动选择下一个可用日期
    if (newDisabledDates.includes(currentDate.value)) {
      currentDate.value = getNextAvailableDate(newDisabledDates, currentDate.value)
    }
  },
  { deep: true },
)

// 方法
const previousDate = () => {
  const currentDateObj = parseISO(currentDate.value)
  const previousDateObj = subDays(currentDateObj, 1)
  const newDateStr = format(previousDateObj, 'yyyy-MM-dd')

  if (disabledDate(previousDateObj)) {
    ElMessage.warning('该日期已有记录，请选择其他日期')
    return
  }

  currentDate.value = newDateStr
}

const nextDate = () => {
  const currentDateObj = parseISO(currentDate.value)
  const nextDateObj = addDays(currentDateObj, 1)
  const newDateStr = format(nextDateObj, 'yyyy-MM-dd')

  if (disabledDate(nextDateObj)) {
    ElMessage.warning('该日期已有记录，请选择其他日期')
    return
  }

  currentDate.value = newDateStr
}

const handleDateChange = (date) => {
  currentDate.value = date
}
</script>

<style scoped>
.meal-date-filter-container {
  transition: all 0.3s ease;
}

.meal-date-filter-container:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: rgb(251 207 232);
}

.date-nav-btn {
  width: 32px;
  height: 32px;
  transition: all 0.2s;
}

:deep(.el-date-editor) {
  border-color: #d1d5db;
  transition: all 0.2s;
}

:deep(.el-date-editor:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-editor.is-active) {
  border-color: rgb(236 72 153);
}
</style>
