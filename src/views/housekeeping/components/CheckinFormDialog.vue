<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="checkin-form-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="completeFormRules"
        label-width="130px"
        class="checkin-form"
      >
        <!-- 母婴关联区 -->
        <div class="form-section mb-6">
          <h4 class="section-title">母婴关联信息</h4>
          <!-- 产妇信息 -->
          <div class="mb-6">
            <h5 class="subsection-title">
              <el-icon class="mr-2 text-pink-500"><Female /></el-icon>
              产妇信息
            </h5>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <el-form-item label="选择产妇" prop="maternity_id" required>
                <!-- 编辑模式：显示产妇姓名-电话号码 -->
                <el-input
                  v-if="props.mode === 'edit'"
                  :value="maternityDisplayText"
                  class="w-full"
                  disabled
                />
                <!-- 新增模式：下拉选择 -->
                <el-select
                  v-else
                  v-model="form.maternity_id"
                  placeholder="请选择产妇"
                  class="w-full"
                  :loading="
                    baseDataStore.maternitys.isLoading() || baseDataStore.maternitys.searchLoading
                  "
                  filterable
                  remote
                  :remote-method="baseDataStore.maternitys.performSearch"
                  :clearable="true"
                  reserve-keyword
                  remote-show-suffix
                  @clear="baseDataStore.maternitys.clearSearch"
                >
                  <el-option
                    v-for="option in baseDataStore.maternitys.getDisplayOptions()"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="预产期" prop="expected_delivery_date" required>
                <el-date-picker
                  v-model="form.expected_delivery_date"
                  type="date"
                  placeholder="选择预产期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  class="w-full"
                />
              </el-form-item>
              <el-form-item label="实际分娩日期" prop="actual_delivery_date">
                <el-date-picker
                  v-model="form.actual_delivery_date"
                  type="date"
                  placeholder="选择实际分娩日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  class="w-full"
                />
              </el-form-item>
              <el-form-item label="分娩医院" prop="delivery_hospital" required>
                <el-input v-model="form.delivery_hospital" placeholder="请输入分娩医院" />
              </el-form-item>
              <el-form-item label="分娩方式" prop="delivery_method" required>
                <el-select
                  v-model="form.delivery_method"
                  placeholder="请选择分娩方式"
                  class="w-full"
                >
                  <el-option
                    v-for="option in deliveryMethodOptions"
                    :key="option.value"
                    :label="option.text"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="孕周" prop="pregnancy_week" required>
                <el-input-number v-model="form.pregnancy_week" :min="1" :max="50" class="w-full" />
              </el-form-item>
            </div>

            <el-form-item label="过敏史(产妇)" prop="allergy_history">
              <el-input
                v-model="form.allergy_history"
                type="textarea"
                :rows="2"
                placeholder="请输入产妇过敏史（如无请填写无）"
              />
            </el-form-item>
          </div>

          <!-- 新生儿信息 -->
          <div v-if="props.mode !== 'edit'" class="mb-4">
            <div class="flex items-center justify-between mb-4">
              <h5 class="subsection-title">
                <el-icon class="mr-2 text-blue-500"><Star /></el-icon>
                新生儿信息
              </h5>
              <el-button
                type="primary"
                size="small"
                @click="addBaby"
                class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
              >
                <el-icon class="mr-1"><Plus /></el-icon>
                添加新生儿
              </el-button>
            </div>

            <div
              v-for="(baby, index) in form.baby_list"
              :key="index"
              class="baby-item border border-gray-200 rounded-lg p-4 mb-4 bg-gray-50"
            >
              <div class="flex items-center justify-between mb-3">
                <span class="font-medium text-gray-700">新生儿 {{ index + 1 }}</span>
                <el-button type="danger" size="small" text @click="removeBaby(index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <el-form-item :label="`新生儿${index + 1}姓名`" :prop="`baby_list.${index}.name`">
                  <el-input v-model="baby.name" placeholder="请输入新生儿姓名" />
                </el-form-item>
                <el-form-item :label="`性别`" :prop="`baby_list.${index}.gender`">
                  <el-radio-group v-model="baby.gender">
                    <el-radio
                      v-for="option in genderOptions"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.text }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <el-form-item :label="`出生日期`" :prop="`baby_list.${index}.birth_time`">
                  <el-date-picker
                    v-model="baby.birth_time"
                    type="date"
                    placeholder="选择出生日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="w-full"
                  />
                </el-form-item>
                <el-form-item :label="`出生体重(g)`" :prop="`baby_list.${index}.birth_weight`">
                  <el-input-number v-model="baby.birth_weight" :min="0" class="w-full" />
                </el-form-item>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <el-form-item :label="`出生孕周`" :prop="`baby_list.${index}.birth_week`">
                  <el-input-number
                    v-model="baby.birth_week"
                    :min="20"
                    :max="45"
                    :precision="1"
                    class="w-full"
                    placeholder="周"
                  />
                </el-form-item>
                <el-form-item :label="`身长(cm)`" :prop="`baby_list.${index}.birth_length`">
                  <el-input-number v-model="baby.birth_length" :min="0" class="w-full" />
                </el-form-item>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <el-form-item :label="`过敏史`" :prop="`baby_list.${index}.allergy_history`">
                  <el-input
                    v-model="baby.allergy_history"
                    placeholder="请输入过敏史（如无请填写无）"
                  />
                </el-form-item>
                <el-form-item :label="`手牌号`" :prop="`baby_list.${index}.hand_card_number`">
                  <el-input v-model="baby.hand_card_number" placeholder="请输入手牌号" />
                </el-form-item>
              </div>
            </div>
          </div>
        </div>

        <!-- 入住信息区 -->
        <div class="form-section mb-6">
          <h4 class="section-title">入住信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="预计入住日期" prop="expected_check_in_date">
              <el-date-picker
                v-model="form.expected_check_in_date"
                type="date"
                placeholder="选择预计入住日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="实际入住日期" prop="actual_check_in_date">
              <el-date-picker
                v-model="form.actual_check_in_date"
                type="date"
                placeholder="选择实际入住日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="入住时长(天)" prop="residence_days" required>
              <el-input-number v-model="form.residence_days" :min="1" class="w-full" />
            </el-form-item>
            <el-form-item label="房间号" prop="room">
              <el-select
                v-model="form.room"
                placeholder="系统推荐或手动选择"
                class="w-full"
                clearable
              >
                <el-option
                  v-for="option in roomOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="选择套餐" prop="cost_info.package" required>
              <el-select
                v-model="form.cost_info.package"
                placeholder="请选择套餐"
                class="w-full"
                :loading="baseDataStore.packages.isLoading()"
                @change="handlePackageChange"
              >
                <el-option
                  v-for="option in packageOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="合同编号" prop="contract_number" required>
              <el-input v-model="form.contract_number" placeholder="请输入合同编号" />
            </el-form-item>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="入住来源" prop="check_in_source" required>
              <el-select v-model="form.check_in_source" placeholder="请选择入住来源" class="w-full">
                <el-option
                  v-for="option in checkInSourceOptions"
                  :key="option.value"
                  :label="option.text"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="入住状态" prop="check_in_status" required>
              <el-select v-model="form.check_in_status" placeholder="请选择入住状态" class="w-full">
                <el-option label="已预定" value="RESERVED" />
                <el-option label="已入住" value="CHECKED_IN" />
                <el-option label="已退房" value="CHECKED_OUT" />
              </el-select>
            </el-form-item>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="主管护士" prop="chief_nurse" required>
              <el-select
                v-model="form.chief_nurse"
                placeholder="请选择主管护士"
                class="w-full"
                :loading="baseDataStore.nurses.isLoading() || baseDataStore.nurses.searchLoading"
                filterable
                remote
                :remote-method="baseDataStore.nurses.performSearch"
                :clearable="true"
                reserve-keyword
                remote-show-suffix
                @clear="baseDataStore.nurses.clearSearch"
              >
                <el-option
                  v-for="option in baseDataStore.nurses.getDisplayOptions()"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="特别关注" prop="need_attention">
              <el-switch v-model="form.need_attention" />
            </el-form-item>
          </div>
        </div>

        <!-- 费用信息区 -->
        <div v-if="props.mode !== 'edit'" class="form-section mb-6">
          <h4 class="section-title">费用信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="套餐价格" prop="" required>
              <el-input :value="packagePrice" class="w-full" readonly />
            </el-form-item>
            <el-form-item label="押金金额" prop="cost_info.deposit_amount" required>
              <el-input-number
                v-model.number="form.cost_info.deposit_amount"
                :min="0"
                :precision="2"
                class="w-full"
                controls-position="right"
              />
            </el-form-item>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="定金金额" prop="cost_info.earnest_amount" required>
              <el-input-number
                v-model.number="form.cost_info.earnest_amount"
                :min="0"
                :precision="2"
                class="w-full"
                controls-position="right"
              />
            </el-form-item>
            <el-form-item label="应付金额" prop="cost_info.payable_amount">
              <el-input-number
                v-model.number="form.cost_info.payable_amount"
                :min="0"
                :precision="2"
                class="w-full"
                controls-position="right"
              />
            </el-form-item>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="已付金额" prop="cost_info.paid_amount" required>
              <el-input-number
                v-model.number="form.cost_info.paid_amount"
                :min="0"
                :precision="2"
                class="w-full"
                controls-position="right"
              />
            </el-form-item>
            <el-form-item label="剩余尾款" prop="cost_info.remaining_amount">
              <el-input-number
                v-model.number="form.cost_info.remaining_amount"
                :min="0"
                :precision="2"
                readonly
                class="w-full"
                controls-position="right"
              />
            </el-form-item>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="支付方式" prop="cost_info.payment_method" required>
              <el-select
                v-model="form.cost_info.payment_method"
                placeholder="请选择支付方式"
                class="w-full"
              >
                <el-option
                  v-for="option in paymentMethodOptions"
                  :key="option.value"
                  :label="option.text"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="费用备注" prop="cost_info.remark">
              <el-input v-model="form.cost_info.remark" placeholder="请输入费用备注" />
            </el-form-item>
          </div>
        </div>

        <!-- 合同上传区 -->
        <div class="form-section mb-6">
          <h4 class="section-title">合同上传</h4>
          <el-form-item label="合同文件" prop="contract_file">
            <FileUpload
              :file-types="['jpg', 'png', 'pdf']"
              :max-size="10"
              :multiple="true"
              :limit="5"
              v-model="form.contract_file"
              :urls="form.contract_file_urls"
              action="file/maternity-contract/upload/"
              field="contract_file"
              upload-text="上传合同"
              custom-tip-text="支持JPG、PNG、PDF格式，最多上传5个文件，每个文件不超过10MB（选填）"
            />
          </el-form-item>
          <el-form-item label="合同备注" prop="contract_remark">
            <el-input
              v-model="form.contract_remark"
              type="textarea"
              :rows="3"
              placeholder="请输入合同相关备注..."
            />
          </el-form-item>
        </div>

        <!-- 分娩医院出院记录 -->
        <div class="form-section mb-6">
          <h4 class="section-title">分娩医院出院记录</h4>
          <el-form-item label="出院记录文件" prop="discharge_records_file">
            <FileUpload
              :file-types="['jpg', 'png', 'pdf']"
              :max-size="10"
              :multiple="true"
              :limit="10"
              action="file/maternity-discharge-records/upload/"
              field="discharge_records_file"
              v-model="form.discharge_records_file"
              :urls="form.discharge_records_file_urls"
              upload-text="上传出院记录"
              custom-tip-text="支持JPG、PNG、PDF格式，最多上传10个文件，每个文件不超过10MB（选填）"
            />
          </el-form-item>
          <el-form-item label="出院记录备注" prop="discharge_records_remark">
            <el-input
              v-model="form.discharge_records_remark"
              type="textarea"
              :rows="3"
              placeholder="请输入出院记录相关备注..."
            />
          </el-form-item>
        </div>

        <!-- SOP确认 -->
        <div class="form-section">
          <h4 class="section-title">SOP确认</h4>
          <div class="sop-checklist mb-3">
            <el-checkbox
              v-for="(item, index) in sopItems"
              :key="index"
              v-model="form.sopChecklist[item.key]"
              :value="item.key"
              class="block"
            >
              {{ item.label }}
            </el-checkbox>
          </div>
          <el-alert title="处理分娩通知与预排房的协调" type="info" :closable="false" class="mt-4">
            <template #default>
              系统提示预排房信息，确认实际入住时间是否与预排房相符，若不符，则根据流程图逻辑处理房间调整
            </template>
          </el-alert>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleSave" :loading="saveLoading">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Female, Star, Plus, Delete } from '@element-plus/icons-vue'
import FileUpload from '@/components/FileUpload.vue'
import {
  GENDER_MAP,
  DELIVERY_METHOD_MAP,
  CHECK_IN_SOURCE_MAP,
  PAYMENT_METHOD_MAP,
} from '@/utils/constants.js'
import { useBaseDataStore } from '@/stores/baseData.js'
import { post, get, put } from '@/utils/request'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: 'create', // create, edit
  },
  checkinId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'submit'])

// 基础数据store
const baseDataStore = useBaseDataStore()

// 表单引用
const formRef = ref()

// 加载状态
const loading = ref(false)

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 弹窗标题
const dialogTitle = computed(() => {
  const titleMap = {
    create: '新增预约/登记',
    edit: '修改预约信息',
  }
  return titleMap[props.mode] || '入住办理'
})

const saveLoading = ref(false)

// 表单数据 - 直接使用后端字段格式
const form = reactive({
  // 产妇ID和信息
  maternity_id: null, // 选择的产妇ID
  maternity: null, // 产妇信息（编辑模式下用于显示）

  // 分娩信息
  expected_delivery_date: null, // 预产期
  actual_delivery_date: null, // 实际分娩日期
  delivery_hospital: '', // 分娩医院
  delivery_method: null, // 分娩方式
  pregnancy_week: null, // 孕周
  allergy_history: '', // 过敏史(产妇)

  // 入住信息
  expected_check_in_date: null, // 预计入住日期
  actual_check_in_date: null, // 实际入住日期
  residence_days: null, // 入住时长(天)
  room: null, // 房间号
  contract_number: '', // 合同编号
  check_in_source: '', // 入住来源
  check_in_status: 'RESERVED', // 入住状态
  need_attention: false, // 特别关注
  chief_nurse: null, // 主管护士

  // 新生儿信息
  baby_list: [],

  // 费用信息 (cost_info)
  cost_info: {
    package: null, // 套餐ID
    deposit_amount: null, // 押金金额
    earnest_amount: null, // 定金金额
    payable_amount: null, // 套餐价格
    paid_amount: null, // 已付金额
    payment_method: '', // 支付方式
    remark: '', // 费用备注
  },

  // 文件上传
  contract_file: [], // 合同文件
  contract_remark: '', // 合同备注
  discharge_records_file: [], // 出院记录文件
  discharge_records_remark: '', // 出院记录备注

  // SOP确认
  sopChecklist: {
    facility: false,
    admissionForm: false,
    nursingForm: false,
    contract: false,
  },
})

// 选项数据
const genderOptions = Object.values(GENDER_MAP)
const deliveryMethodOptions = Object.values(DELIVERY_METHOD_MAP)
const checkInSourceOptions = Object.values(CHECK_IN_SOURCE_MAP)
const paymentMethodOptions = Object.values(PAYMENT_METHOD_MAP)

// 套餐选项（使用computed确保响应式更新）
const packageOptions = computed(() => baseDataStore.packages.getOptions())
const roomOptions = computed(() => baseDataStore.rooms.getOptions())

// 计算字段
// const totalPayable = computed(() => {
//   return (form.cost_info.payable_amount || 0) + (form.cost_info.deposit_amount || 0)
// })

// 编辑模式下产妇显示文本
const maternityDisplayText = computed(() => {
  if (props.mode === 'edit' && form.maternity) {
    return `${form.maternity.name}-${form.maternity.phone}`
  }
  return ''
})

// SOP项目
const sopItems = [
  { key: 'facility', label: '介绍设施设备' },
  { key: 'admissionForm', label: '填写入住评估单指引' },
  { key: 'nursingForm', label: '填写护理评估单指引' },
  { key: 'contract', label: '确认合同（尾款）' },
]

// 表单验证规则
const formRules = {
  // 产妇选择必填验证
  maternity_id: [{ required: true, message: '请选择产妇', trigger: 'change' }],

  // 分娩信息必填验证
  expected_delivery_date: [{ required: true, message: '请选择预产期', trigger: 'change' }],
  actual_delivery_date: [{ required: false, message: '请选择实际分娩日期', trigger: 'change' }],
  delivery_hospital: [{ required: true, message: '请输入分娩医院', trigger: 'blur' }],
  delivery_method: [{ required: true, message: '请选择分娩方式', trigger: 'change' }],
  pregnancy_week: [{ required: true, message: '请输入孕周', trigger: 'blur' }],
  allergy_history: [{ required: false, message: '请输入过敏史（如无请填写无）', trigger: 'blur' }],

  // 入住信息必填验证
  expected_check_in_date: [
    {
      validator: (rule, value, callback) => {
        // 只有在选择了房间时，预计入住日期才是必填的
        if (form.room && !value) {
          callback(new Error('选择房间后，预计入住日期为必填项'))
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
  actual_check_in_date: [{ required: false, message: '请选择实际入住日期', trigger: 'change' }],
  residence_days: [{ required: true, message: '请输入入住时长', trigger: 'blur' }],
  room: [{ required: false, message: '请选择房间号', trigger: 'change' }],
  contract_number: [{ required: true, message: '请输入合同编号', trigger: 'blur' }],
  check_in_source: [{ required: true, message: '请选择入住来源', trigger: 'change' }],
  check_in_status: [{ required: true, message: '请选择入住状态', trigger: 'change' }],
  chief_nurse: [{ required: true, message: '请选择主管护士', trigger: 'change' }],

  // 费用信息必填验证
  'cost_info.package': [{ required: true, message: '请选择套餐', trigger: 'change' }],
  'cost_info.payable_amount': [{ required: true, message: '请输入套餐价格', trigger: 'blur' }],
  'cost_info.deposit_amount': [{ required: true, message: '请输入押金金额', trigger: 'blur' }],
  'cost_info.earnest_amount': [{ required: true, message: '请输入定金金额', trigger: 'blur' }],
  'cost_info.paid_amount': [{ required: true, message: '请输入已付金额', trigger: 'blur' }],
  'cost_info.payment_method': [{ required: true, message: '请选择支付方式', trigger: 'change' }],
  'cost_info.remark': [{ required: false, message: '请输入费用备注', trigger: 'blur' }],

  // 合同文件选填验证
  contract_file: [{ required: false, message: '请上传合同文件', trigger: 'change' }],
  contract_remark: [{ required: false, message: '请输入合同备注', trigger: 'blur' }],

  // 出院记录选填验证
  discharge_records_file: [{ required: false, message: '请上传出院记录文件', trigger: 'change' }],
  discharge_records_remark: [{ required: false, message: '请输入出院记录备注', trigger: 'blur' }],
}

// 动态添加新生儿验证规则
const getDynamicFormRules = () => {
  const rules = { ...formRules }

  // 为每个新生儿添加验证规则
  form.baby_list.forEach((baby, index) => {
    rules[`baby_list.${index}.name`] = [
      { required: false, message: '请输入新生儿姓名', trigger: 'blur' },
    ]
    rules[`baby_list.${index}.gender`] = [
      { required: false, message: '请选择新生儿性别', trigger: 'change' },
    ]
    rules[`baby_list.${index}.birth_time`] = [
      { required: false, message: '请选择出生日期', trigger: 'change' },
    ]
    rules[`baby_list.${index}.birth_weight`] = [
      { required: false, message: '请输入出生体重', trigger: 'blur' },
    ]
    rules[`baby_list.${index}.birth_week`] = [
      { required: false, message: '请输入出生孕周', trigger: 'blur' },
    ]
    rules[`baby_list.${index}.birth_length`] = [
      { required: false, message: '请输入身长', trigger: 'blur' },
    ]
    rules[`baby_list.${index}.allergy_history`] = [
      { required: false, message: '请输入过敏史（如无请填写无）', trigger: 'blur' },
    ]
    rules[`baby_list.${index}.hand_card_number`] = [
      { required: false, message: '请输入手牌号', trigger: 'blur' },
    ]
  })

  return rules
}

// 使用计算属性获取完整的表单验证规则
const completeFormRules = computed(() => getDynamicFormRules())

// 新增新生儿
const addBaby = () => {
  form.baby_list.push({
    name: '',
    gender: null,
    birth_time: '',
    birth_weight: null,
    birth_week: null,
    birth_length: null,
    allergy_history: '',
    hand_card_number: null,
  })
}

// 删除新生儿
const removeBaby = (index) => {
  form.baby_list.splice(index, 1)
}

const packagePrice = computed(() => {
  if (form.cost_info.package) {
    const selectedPackage = packageOptions.value.find(
      (option) => option.value === form.cost_info.package,
    )
    return selectedPackage?.origin?.price || 0
  }
  return 0
})

// 处理套餐变化
const handlePackageChange = (packageId) => {
  if (packageId) {
    // 从packageOptions中找到选中的套餐，获取其origin信息
    const selectedPackage = packageOptions.value.find((option) => option.value === packageId)
    if (selectedPackage && selectedPackage.origin) {
      // 如果入住时长为空，则将套餐的时长填入
      if (!form.residence_days && selectedPackage.origin.stay_days) {
        form.residence_days = parseInt(selectedPackage.origin.stay_days) || null
      }
    }
  } else {
    // 如果没有选择套餐，清空价格
    // packagePrice.value = null
    // form.cost_info.payable_amount = null
  }
}

// 获取入住详情数据（用于编辑模式）
const fetchCheckinDetail = async (checkinId) => {
  if (!checkinId) return

  loading.value = true
  try {
    const response = await get(`customer-service/maternity-admission/detail/${checkinId}/`)
    if (response) {
      // 转换接口数据为表单格式并填充
      const processedData = transformAPIDataToForm(response)
      Object.assign(form, processedData)
    }
  } catch (error) {
    console.error('获取入住详情失败:', error)
    ElMessage.error('获取入住详情失败')
  } finally {
    loading.value = false
  }
}

// 数据转换函数：将表单数据转换为接口格式
const transformFormDataToAPI = (formData) => {
  // 表单结构已经与后端一致，只需要处理计算字段
  const result = { ...formData }

  // 处理房间号：如果有值且不为null则转为数字，否则为null
  if (result.room && result.room !== null && result.room !== '') {
    // 房间号有效，保持原值
  } else {
    result.room = null
  }

  // 处理 baby_list：如果只有 name 有值，则设为空数组
  if (result.baby_list && Array.isArray(result.baby_list)) {
    const validBabies = result.baby_list.filter((baby) => {
      // 检查除了name之外的关键字段是否有值
      const hasOtherFields =
        baby.gender ||
        baby.birth_time ||
        baby.birth_weight ||
        baby.birth_length ||
        (baby.allergy_history && baby.allergy_history.trim()) ||
        (baby.hand_card_number && baby.hand_card_number.trim())

      return hasOtherFields
    })

    result.baby_list = validBabies
  }

  delete result.sopChecklist

  // 计算剩余金额
  // if (result.cost_info) {
  //   result.cost_info.remaining_amount = totalPayable.value - (result.cost_info.paid_amount || 0)
  // }

  return result
}

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  // 后端数据结构与表单一致，只需处理特殊情况
  const formData = { ...apiData }

  // 处理产妇ID，如果有maternity对象，提取其ID，并保留产妇信息用于显示
  if (formData.maternity && formData.maternity.uid) {
    formData.maternity_id = formData.maternity.uid
    // 保留产妇信息用于编辑模式显示
    // formData.maternity 已经存在，无需额外处理
  } else if (!formData.maternity_id) {
    formData.maternity_id = null
  }

  if (!formData.cost_info) {
    formData.cost_info = {
      package: null,
      deposit_amount: null,
      earnest_amount: null,
      payable_amount: null,
      paid_amount: null,
      payment_method: '',
      remark: '',
    }
  } else {
    // 处理套餐信息，如果有package_details，提取package的ID
    if (formData.cost_info.package_details && formData.cost_info.package_details.id) {
      formData.cost_info.package = formData.cost_info.package_details.id
    }

    // 确保所有金额字段都是数值类型，而不是字符串
    const numericFields = [
      'deposit_amount',
      'earnest_amount',
      'payable_amount',
      'paid_amount',
      'remaining_amount',
    ]
    numericFields.forEach((field) => {
      if (formData.cost_info[field] !== null && formData.cost_info[field] !== undefined) {
        const numValue = parseFloat(formData.cost_info[field])
        formData.cost_info[field] = isNaN(numValue) ? null : numValue
      }
    })
  }

  if (!formData.baby_list) {
    formData.baby_list = []
  }

  if (formData.baby_list.length > 0) {
    // 处理日期时间格式，只保留日期部分，并确保数值字段是正确的类型
    formData.baby_list = formData.baby_list.map((baby) => ({
      ...baby,
      birth_time: baby.birth_time ? baby.birth_time.split(' ')[0] : '',
      birth_weight:
        baby.birth_weight !== null && baby.birth_weight !== undefined
          ? isNaN(parseFloat(baby.birth_weight))
            ? null
            : parseFloat(baby.birth_weight)
          : null,
      birth_week:
        baby.birth_week !== null && baby.birth_week !== undefined
          ? isNaN(parseFloat(baby.birth_week))
            ? null
            : parseFloat(baby.birth_week)
          : null,
      birth_length:
        baby.birth_length !== null && baby.birth_length !== undefined
          ? isNaN(parseFloat(baby.birth_length))
            ? null
            : parseFloat(baby.birth_length)
          : null,
      hand_card_number: baby.hand_card_number,
    }))
  }

  // 处理房间信息
  if (formData.room) {
    if (typeof formData.room === 'object' && formData.room.rid) {
      // 如果room是对象，提取ID
      formData.room = formData.room.rid
    }
  } else {
    // 确保room为null而不是空字符串
    formData.room = null
  }

  // 确保文件数组存在
  formData.contract_file = formData.contract_file || []
  formData.discharge_records_file = formData.discharge_records_file || []

  // 确保其他数值字段的类型正确
  const otherNumericFields = ['pregnancy_week', 'residence_days']
  otherNumericFields.forEach((field) => {
    if (formData[field] !== null && formData[field] !== undefined) {
      const numValue = parseInt(formData[field])
      formData[field] = isNaN(numValue) ? null : numValue
    }
  })

  // SOP确认默认值
  formData.sopChecklist = {
    facility: false,
    admissionForm: false,
    nursingForm: false,
    contract: false,
  }

  return formData
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 保存
const handleSave = async () => {
  console.log('form.contract_file', form.contract_file)
  try {
    saveLoading.value = true
    console.log('form', form)
    await formRef.value.validate()
    const apiData = transformFormDataToAPI(form)
    console.log('apiData', apiData)

    let res
    if (props.mode === 'edit' && props.checkinId) {
      // 编辑模式：使用更新接口
      res = await put(`customer-service/maternity-admission/update/${props.checkinId}/`, apiData)
    } else {
      // 创建模式：使用创建接口，URL包含产妇ID
      res = await post(`customer-service/maternity-admission/create/${form.maternity_id}/`, apiData)
    }

    ElMessage.success('保存成功')
    emit('submit', { ...res, action: 'save' })
  } catch (error) {
    showErrorTip(error)
  } finally {
    saveLoading.value = false
  }
}

// 监听费用变化，自动计算剩余金额
watch(
  () => [form.cost_info.payable_amount, form.cost_info.paid_amount],
  () => {
    const totalPayable = form.cost_info.payable_amount || 0
    form.cost_info.remaining_amount = totalPayable - (form.cost_info.paid_amount || 0)
  },
  { deep: true },
)

// 监听房间变化，重新验证相关字段
watch(
  () => form.room,
  () => {
    nextTick(() => {
      // 重新验证预计入住日期字段
      formRef.value?.validateField('expected_check_in_date')
    })
  },
)

// 重置表单数据
const resetForm = async () => {
  // 先清除验证状态
  // await nextTick()
  // formRef.value?.clearValidate()

  // 重置表单到初始状态
  Object.assign(form, {
    // 产妇ID和信息
    maternity_id: null, // 选择的产妇ID
    maternity: null, // 产妇信息（编辑模式下用于显示）

    // 分娩信息
    expected_delivery_date: null, // 预产期
    actual_delivery_date: null, // 实际分娩日期
    delivery_hospital: '', // 分娩医院
    delivery_method: null, // 分娩方式
    pregnancy_week: null, // 孕周
    allergy_history: '', // 过敏史(产妇)

    // 入住信息
    expected_check_in_date: null, // 预计入住日期
    actual_check_in_date: null, // 实际入住日期
    residence_days: null, // 入住时长(天)
    room: null, // 房间号
    contract_number: '', // 合同编号
    check_in_source: '', // 入住来源
    check_in_status: 'RESERVED', // 入住状态
    need_attention: false, // 特别关注
    chief_nurse: null, // 主管护士

    // 新生儿信息
    baby_list: [],

    // 费用信息 (cost_info)
    cost_info: {
      package: null, // 套餐ID
      deposit_amount: null, // 押金金额
      earnest_amount: null, // 定金金额
      payable_amount: null, // 套餐价格
      paid_amount: null, // 已付金额
      payment_method: '', // 支付方式
      remark: '', // 费用备注
    },

    // 文件上传
    contract_file: [], // 合同文件
    contract_remark: '', // 合同备注
    discharge_records_file: [], // 出院记录文件
    discharge_records_remark: '', // 出院记录备注

    // SOP确认
    sopChecklist: {
      facility: false,
      admissionForm: false,
      nursingForm: false,
      contract: false,
    },
  })

  // 重置搜索状态
  baseDataStore.nurses.clearSearch()
  baseDataStore.maternitys.clearSearch()

  // 再次确保清除验证状态（数据重置后）
  await nextTick()
  formRef.value?.clearValidate()
}

// 滚动到顶部
const scrollToTop = async () => {
  await nextTick()
  const scrollContainer = document.querySelector('.checkin-form-dialog .max-h-\\[70vh\\]')
  if (scrollContainer) {
    scrollContainer.scrollTop = 0
  }
}

// 监听弹窗显示状态，处理编辑模式数据获取
watch(
  () => props.modelValue,
  async (visible) => {
    if (visible) {
      await scrollToTop()

      if (props.mode === 'edit' && props.checkinId) {
        // 编辑模式：获取详情数据
        await fetchCheckinDetail(props.checkinId)
        // 数据加载完成后清除验证状态
        await nextTick()
        formRef.value?.clearValidate()
      } else if (props.mode === 'create') {
        // 创建模式：确保表单验证状态清洁
        await nextTick()
        formRef.value?.clearValidate()
      }
    } else {
      // 对话框关闭时清空所有数据
      await resetForm()
    }
  },
)
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.subsection-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

.sop-checklist {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-input-number .el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input-number .el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input-number .el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-date-editor.el-input) {
  transition: all 0.2s;
}

:deep(.el-date-editor.el-input:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-editor.el-input.is-focus .el-input__wrapper) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
