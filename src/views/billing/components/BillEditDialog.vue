<template>
  <el-dialog
    v-model="visible"
    title="编辑结算单"
    width="1000px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    class="bill-edit-dialog"
  >
    <div v-loading="loading" class="edit-content max-h-[70vh] overflow-y-auto">
      <div v-if="billData" class="space-y-6">
        <!-- 基本信息展示 -->
        <div class="info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
            <div class="info-item">
              <label>结算单号：</label>
              <span class="font-mono">{{ billData.bid }}</span>
            </div>
            <div class="info-item">
              <label>产妇姓名：</label>
              <span>{{ billData.maternity_admission.maternity_name }}</span>
            </div>
            <div class="info-item">
              <label>房间号：</label>
              <span>{{ billData.maternity_admission.room.room_number }}</span>
            </div>
            <div class="info-item">
              <label>套餐名称：</label>
              <span>{{ billData.package.name }}</span>
            </div>
            <div class="info-item">
              <label>入住日期：</label>
              <span>{{ billData.maternity_admission.check_in_date }}</span>
            </div>
            <div class="info-item">
              <label>出院日期：</label>
              <span>{{ billData.maternity_admission.check_out_date }}</span>
            </div>
          </div>
        </div>

        <!-- 结算单编辑表单 -->
        <div class="info-section">
          <h3 class="section-title">结算单编辑</h3>
          <el-form
            ref="billFormRef"
            :model="billForm"
            :rules="billRules"
            label-width="120px"
            class="bill-form"
          >
            <div class="grid grid-cols-2 gap-4">
              <el-form-item label="套餐选择" prop="package">
                <el-select 
                  v-model="billForm.package" 
                  placeholder="请选择套餐"
                  class="w-full"
                  @change="handlePackageChange"
                >
                  <el-option
                    v-for="pkg in packageList"
                    :key="pkg.rid"
                    :label="pkg.name"
                    :value="pkg.rid"
                  >
                    <div class="flex justify-between">
                      <span>{{ pkg.name }}</span>
                      <span class="text-gray-500">¥{{ parseFloat(pkg.price).toLocaleString() }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="套餐价格" prop="package_price">
                <el-input-number
                  v-model="billForm.package_price"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  class="w-full"
                />
              </el-form-item>

              <el-form-item label="押金金额" prop="deposit_amount">
                <el-input-number
                  v-model="billForm.deposit_amount"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  class="w-full"
                />
              </el-form-item>

              <el-form-item label="定金金额" prop="earnest_amount">
                <el-input-number
                  v-model="billForm.earnest_amount"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  class="w-full"
                />
              </el-form-item>

              <el-form-item label="应付金额" prop="payable_amount">
                <el-input-number
                  v-model="billForm.payable_amount"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  class="w-full"
                />
              </el-form-item>

              <el-form-item label="已付金额" prop="paid_amount">
                <el-input-number
                  v-model="billForm.paid_amount"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  class="w-full"
                />
              </el-form-item>

              <el-form-item label="剩余尾款" prop="remaining_amount">
                <el-input-number
                  v-model="billForm.remaining_amount"
                  :precision="2"
                  controls-position="right"
                  class="w-full"
                />
              </el-form-item>

              <el-form-item label="支付方式" prop="payment_method">
                <el-select
                  v-model="billForm.payment_method"
                  placeholder="请选择支付方式"
                  class="w-full payment-method-select"
                  multiple
                >
                  <el-option label="现金" value="CASH" />
                  <el-option label="银行卡" value="BANK_CARD" />
                  <el-option label="信用卡" value="CREDIT_CARD" />
                  <el-option label="微信支付" value="WECHAT_PAY" />
                  <el-option label="支付宝支付" value="ALIPAY_PAY" />
                  <el-option label="其他" value="OTHER" />
                </el-select>
              </el-form-item>

              <el-form-item label="支付状态" prop="payment_status">
                <el-select 
                  v-model="billForm.payment_status" 
                  placeholder="请选择支付状态"
                  class="w-full"
                >
                  <el-option label="已全款支付" value="FULL_PAID" />
                  <el-option label="部分支付" value="PARTIAL_PAID" />
                  <el-option label="未支付" value="UNPAID" />
                  <el-option label="已退款" value="REFUNDED" />
                </el-select>
              </el-form-item>
            </div>

            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="billForm.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- 续住费用信息 -->
        <div class="info-section">
          <div class="flex items-center justify-between mb-4">
            <h3 class="section-title mb-0">续住费用信息</h3>
          </div>
          
          <div v-if="billData.ma_renew && billData.ma_renew.length > 0" class="renew-list space-y-3">
            <div
              v-for="(renew, index) in billData.ma_renew"
              :key="index"
              class="renew-item p-4 bg-white border rounded-lg hover:border-pink-300 transition-colors"
            >
              <div class="flex items-center justify-between">
                <div class="grid grid-cols-4 gap-4 flex-1">
                  <div class="info-item">
                    <label>续住单号：</label>
                    <span class="font-mono text-sm">{{ renew.rbid }}</span>
                  </div>
                  <div class="info-item">
                    <label>续住天数：</label>
                    <span>{{ renew.renew_days }} 天</span>
                  </div>
                  <div class="info-item">
                    <label>续住费用：</label>
                    <span class="text-red-600 font-medium">¥{{ parseFloat(renew.renew_cost).toLocaleString() }}</span>
                  </div>
                  <div class="info-item">
                    <label>费用备注：</label>
                    <span>{{ renew.renew_cost_remark || '-' }}</span>
                  </div>
                </div>
                <div class="ml-4">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleEditRenew(renew)"
                    class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
                  >
                    编辑
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          
          <div v-else class="no-renew-data p-8 text-center bg-gray-50 rounded-lg">
            <el-icon class="text-gray-400 text-4xl mb-2">
              <Document />
            </el-icon>
            <p class="text-gray-500">暂无续住费用信息</p>
          </div>
        </div>

        <!-- 费用汇总信息 -->
        <div class="info-section">
          <h3 class="section-title">费用汇总</h3>
          <div class="cost-summary p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div class="grid grid-cols-3 gap-4">
              <div class="info-item">
                <label>套餐价格：</label>
                <span class="font-medium">¥{{ parseFloat(billData.package_price).toLocaleString() }}</span>
              </div>
              <div class="info-item">
                <label>押金金额：</label>
                <span class="font-medium">¥{{ parseFloat(billData.deposit_amount).toLocaleString() }}</span>
              </div>
              <div class="info-item">
                <label>定金金额：</label>
                <span class="font-medium">¥{{ parseFloat(billData.earnest_amount).toLocaleString() }}</span>
              </div>
              <div class="info-item">
                <label>应付金额：</label>
                <span class="font-medium text-blue-600">¥{{ parseFloat(billData.payable_amount).toLocaleString() }}</span>
              </div>
              <div class="info-item">
                <label>已付金额：</label>
                <span class="font-medium text-green-600">¥{{ parseFloat(billData.paid_amount).toLocaleString() }}</span>
              </div>
              <div class="info-item">
                <label>剩余尾款：</label>
                <span 
                  class="font-medium"
                  :class="parseFloat(billData.remaining_amount) < 0 ? 'text-green-600' : 'text-red-600'"
                >
                  ¥{{ parseFloat(billData.remaining_amount).toLocaleString() }}
                  <span v-if="parseFloat(billData.remaining_amount) < 0" class="text-sm text-gray-500 ml-1">(已超付)</span>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 支付信息 -->
        <div class="info-section">
          <h3 class="section-title">支付信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="info-item">
              <label>支付方式：</label>
              <div class="payment-methods">
                <el-tag
                  v-for="method in getPaymentMethodsArray(billData.payment_method)"
                  :key="method"
                  size="small"
                  class="mr-2 mb-1"
                >
                  {{ getPaymentMethodText(method) }}
                </el-tag>
              </div>
            </div>
            <div class="info-item">
              <label>支付状态：</label>
              <el-tag 
                :type="getPaymentStatusType(billData.payment_status)"
                :effect="billData.payment_status === 'FULL_PAID' || billData.payment_status === 'REFUNDED' ? 'dark' : 'plain'"
                size="small"
              >
                {{ getPaymentStatusText(billData.payment_status) }}
              </el-tag>
            </div>
            <div class="info-item col-span-2">
              <label>备注：</label>
              <span>{{ billData.remark || '-' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          type="primary" 
          @click="handleSaveBill"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          保存结算单
        </el-button>
      </div>
    </template>

    <!-- 续住信息编辑弹窗 -->
    <el-dialog
      v-model="renewEditVisible"
      title="编辑续住信息"
      width="500px"
      align-center
      :close-on-click-modal="false"
      class="renew-edit-dialog"
      append-to-body
    >
      <el-form
        ref="renewFormRef"
        :model="renewForm"
        :rules="renewRules"
        label-width="100px"
        class="renew-form"
      >
        <el-form-item label="续住单号">
          <el-input v-model="renewForm.rbid" disabled class="font-mono" />
        </el-form-item>
        
        <el-form-item label="续住天数" prop="renew_days">
          <el-input-number
            v-model="renewForm.renew_days"
            :min="1"
            :max="365"
            :precision="0"
            controls-position="right"
            class="w-full"
          />
        </el-form-item>
        
        <el-form-item label="续住费用" prop="renew_cost">
          <el-input-number
            v-model="renewForm.renew_cost"
            :min="0"
            :precision="2"
            controls-position="right"
            class="w-full"
          />
        </el-form-item>
        
        <el-form-item label="费用备注" prop="renew_cost_remark">
          <el-input
            v-model="renewForm.renew_cost_remark"
            type="textarea"
            :rows="3"
            placeholder="请输入费用备注"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="renewEditVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleSaveRenew"
            :loading="renewSaving"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
          >
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import { ElDialog, ElButton, ElTag, ElForm, ElFormItem, ElInput, ElInputNumber, ElSelect, ElOption, ElIcon, ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { get, put } from '@/utils/request'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  billId: {
    type: String,
    default: ''
  }
})

// 定义事件
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const billData = ref(null)
const packageList = ref([])

// 结算单编辑相关
const billFormRef = ref()
const billForm = reactive({
  package: '',
  package_price: 0,
  deposit_amount: 0,
  earnest_amount: 0,
  payable_amount: 0,
  paid_amount: 0,
  remaining_amount: 0,
  payment_method: [],
  payment_status: '',
  remark: ''
})

// 续住编辑相关
const renewEditVisible = ref(false)
const renewSaving = ref(false)
const renewFormRef = ref()
const currentRenewItem = ref(null)

const renewForm = reactive({
  rbid: '',
  renew_days: 0,
  renew_cost: 0,
  renew_cost_remark: ''
})

// 结算单表单验证规则
const billRules = {
  package: [
    { required: true, message: '请选择套餐', trigger: 'change' }
  ],
  package_price: [
    { required: true, message: '请输入套餐价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '套餐价格不能为负数', trigger: 'blur' }
  ],
  deposit_amount: [
    { required: true, message: '请输入押金金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '押金金额不能为负数', trigger: 'blur' }
  ],
  earnest_amount: [
    { required: true, message: '请输入定金金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '定金金额不能为负数', trigger: 'blur' }
  ],
  payable_amount: [
    { required: true, message: '请输入应付金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '应付金额不能为负数', trigger: 'blur' }
  ],
  paid_amount: [
    { required: true, message: '请输入已付金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '已付金额不能为负数', trigger: 'blur' }
  ],
  payment_method: [
    { required: true, type: 'array', min: 1, message: '请至少选择一种支付方式', trigger: 'change' }
  ],
  payment_status: [
    { required: true, message: '请选择支付状态', trigger: 'change' }
  ]
}

// 续住表单验证规则
const renewRules = {
  renew_days: [
    { required: true, message: '请输入续住天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 365, message: '续住天数必须在1-365天之间', trigger: 'blur' }
  ],
  renew_cost: [
    { required: true, message: '请输入续住费用', trigger: 'blur' },
    { type: 'number', min: 0, message: '续住费用不能为负数', trigger: 'blur' }
  ],
  renew_cost_remark: [
    { required: true, message: '请输入费用备注', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听billId变化，获取详情数据
watch(
  () => props.billId,
  async (newBillId) => {
    if (newBillId && props.visible) {
      await fetchBillDetail(newBillId)
    }
  },
  { immediate: true }
)

// 监听弹窗显示状态
watch(
  () => props.visible,
  async (newVisible) => {
    if (newVisible && props.billId) {
      await Promise.all([
        fetchBillDetail(props.billId),
        fetchPackageList()
      ])
    }
  }
)

// 获取结算单详情
const fetchBillDetail = async (billId) => {
  loading.value = true
  try {
    const data = await get(`/organizational-management/bills/detail/${billId}/`)
    billData.value = data
    
    // 填充表单数据
    Object.assign(billForm, {
      package: data.package.rid,
      package_price: parseFloat(data.package_price),
      deposit_amount: parseFloat(data.deposit_amount),
      earnest_amount: parseFloat(data.earnest_amount),
      payable_amount: parseFloat(data.payable_amount),
      paid_amount: parseFloat(data.paid_amount),
      remaining_amount: parseFloat(data.remaining_amount),
      payment_method: getPaymentMethodsArray(data.payment_method),
      payment_status: data.payment_status,
      remark: data.remark || ''
    })
    
  } catch (error) {
    console.error('获取结算单详情失败:', error)
    ElMessage.error('获取结算单详情失败')
  } finally {
    loading.value = false
  }
}

// 获取套餐列表
const fetchPackageList = async () => {
  try {
    const data = await get('/organizational-management/packages/list/', { 
      page: 1, 
      page_size: 100,
      status: 'ACTIVE'
    })
    packageList.value = data.list
  } catch (error) {
    console.error('获取套餐列表失败:', error)
    ElMessage.error('获取套餐列表失败')
  }
}

// 处理套餐变化
const handlePackageChange = (packageId) => {
  const selectedPackage = packageList.value.find(pkg => pkg.rid === packageId)
  if (selectedPackage) {
    billForm.package_price = parseFloat(selectedPackage.price)
  }
}

// 获取支付方式数组
const getPaymentMethodsArray = (paymentMethod) => {
  if (!paymentMethod) return []

  // 如果是数组，直接返回
  if (Array.isArray(paymentMethod)) {
    return paymentMethod
  }

  // 如果是字符串，尝试解析为数组
  if (typeof paymentMethod === 'string') {
    try {
      // 尝试解析JSON格式的字符串
      const parsed = JSON.parse(paymentMethod)
      return Array.isArray(parsed) ? parsed : [paymentMethod]
    } catch {
      // 如果解析失败，当作单个支付方式处理
      return [paymentMethod]
    }
  }

  return []
}

// 获取支付方式文本
const getPaymentMethodText = (method) => {
  const methodMap = {
    CASH: '现金',
    BANK_CARD: '银行卡',
    CREDIT_CARD: '信用卡',
    WECHAT_PAY: '微信支付',
    ALIPAY_PAY: '支付宝支付',
    OTHER: '其他'
  }
  return methodMap[method] || method
}

// 获取支付方式显示文本（用于显示多个支付方式）
const getPaymentMethodsText = (paymentMethods) => {
  const methods = getPaymentMethodsArray(paymentMethods)
  return methods.map(method => getPaymentMethodText(method)).join('、')
}

// 获取支付状态文本
const getPaymentStatusText = (status) => {
  const statusMap = {
    FULL_PAID: '已全款支付',
    PARTIAL_PAID: '部分支付',
    UNPAID: '未支付',
    REFUNDED: '已退款'
  }
  return statusMap[status] || status
}

// 获取支付状态标签类型
const getPaymentStatusType = (status) => {
  const typeMap = {
    FULL_PAID: 'success',      // 已全款支付 - 绿色
    PARTIAL_PAID: 'warning',   // 已部分支付 - 橙色
    UNPAID: 'danger',          // 未支付 - 红色
    REFUNDED: 'danger',        // 已退款 - 红色（更显眼）
  }
  return typeMap[status] || 'info'
}

// 编辑续住信息
const handleEditRenew = (renewItem) => {
  currentRenewItem.value = renewItem
  Object.assign(renewForm, {
    rbid: renewItem.rbid,
    renew_days: renewItem.renew_days,
    renew_cost: parseFloat(renewItem.renew_cost),
    renew_cost_remark: renewItem.renew_cost_remark || ''
  })
  renewEditVisible.value = true
}

// 保存续住信息
const handleSaveRenew = async () => {
  try {
    await renewFormRef.value.validate()
    
    renewSaving.value = true
    
    const updateData = {
      renew_days: renewForm.renew_days,
      renew_cost: renewForm.renew_cost,
      renew_cost_remark: renewForm.renew_cost_remark
    }
    
    const result = await put(`/organizational-management/renew/update/${renewForm.rbid}/`, updateData)
    
    // 更新本地数据
    if (currentRenewItem.value) {
      currentRenewItem.value.renew_days = result.renew_days
      currentRenewItem.value.renew_cost = result.renew_cost
      currentRenewItem.value.renew_cost_remark = result.renew_cost_remark
    }
    
    ElMessage.success('续住信息更新成功')
    renewEditVisible.value = false
    
  } catch (error) {
    console.error('保存续住信息失败:', error)
    ElMessage.error('保存续住信息失败')
  } finally {
    renewSaving.value = false
  }
}

// 保存结算单
const handleSaveBill = async () => {
  try {
    await billFormRef.value.validate()
    
    saving.value = true
    
    const updateData = {
      package: billForm.package,
      package_price: billForm.package_price,
      deposit_amount: billForm.deposit_amount,
      earnest_amount: billForm.earnest_amount,
      payable_amount: billForm.payable_amount,
      paid_amount: billForm.paid_amount,
      remaining_amount: billForm.remaining_amount,
      payment_method: billForm.payment_method,
      payment_status: billForm.payment_status,
      remark: billForm.remark
    }
    
    await put(`/organizational-management/bills/update/${props.billId}/`, updateData)
    
    ElMessage.success('结算单更新成功')
    emit('save', billData.value)
    
  } catch (error) {
    console.error('保存结算单失败:', error)
    ElMessage.error('保存结算单失败')
  } finally {
    saving.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  billData.value = null
  renewEditVisible.value = false
}
</script>

<style scoped>
.info-section {
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.info-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.info-item {
  display: flex;
  align-items: start;
  gap: 0.5rem;
}

.info-item label {
  font-weight: 500;
  color: #6b7280;
  white-space: nowrap;
  min-width: 80px;
}

.info-item span {
  color: #1f2937;
  word-break: break-all;
}

.renew-item {
  transition: all 0.2s ease;
}

.cost-summary {
  border: 1px solid #93c5fd;
}

.no-renew-data {
  border: 2px dashed #d1d5db;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 续住编辑弹窗样式 */
.renew-edit-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.renew-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.renew-form :deep(.el-input__wrapper) {
  transition: all 0.2s;
}

.renew-form :deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

.renew-form :deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

.renew-form :deep(.el-textarea__inner) {
  transition: all 0.2s;
}

.renew-form :deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

.renew-form :deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

.renew-form :deep(.el-input-number:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

.renew-form :deep(.el-input-number.is-focus .el-input__wrapper) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

/* 结算单编辑表单样式 */
.bill-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.bill-form :deep(.el-input__wrapper) {
  transition: all 0.2s;
}

.bill-form :deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

.bill-form :deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

.bill-form :deep(.el-textarea__inner) {
  transition: all 0.2s;
}

.bill-form :deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

.bill-form :deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

.bill-form :deep(.el-input-number:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

.bill-form :deep(.el-input-number.is-focus .el-input__wrapper) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

.bill-form :deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

/* 确保支付状态标签样式与表格一致 */
:deep(.el-tag) {
  font-weight: normal;
}

/* 已退款 - 红色实心 */
:deep(.el-tag.el-tag--danger.el-tag--dark) {
  color: #fff !important;
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
}

/* 已全款支付 - 绿色实心 */
:deep(.el-tag.el-tag--success.el-tag--dark) {
  color: #fff !important;
  background-color: #2BEA8E !important;
  border-color: #2BEA8E !important;
}

/* 部分支付 - 橙色边框 */
:deep(.el-tag.el-tag--warning.el-tag--plain) {
  color: #e6a23c !important;
  background-color: #fdf6ec !important;
  border-color: #f5dab1 !important;
}

/* 未支付 - 红色边框 */
:deep(.el-tag.el-tag--danger.el-tag--plain) {
  color: #f56c6c !important;
  background-color: #fef0f0 !important;
  border-color: #fbc4c4 !important;
}

/* 支付方式标签样式 */
.payment-methods {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.payment-methods .el-tag {
  background-color: #f0f9ff;
  border-color: #0ea5e9;
  color: #0369a1;
}

/* 支付方式多选框样式 */
.payment-method-select :deep(.el-select__tags) {
  max-width: 100%;
  flex-wrap: wrap;
}

.payment-method-select :deep(.el-tag) {
  margin: 2px 4px 2px 0;
  max-width: none;
}

.payment-method-select :deep(.el-select__input-wrapper) {
  min-height: auto;
  padding: 4px 8px;
}

.payment-method-select :deep(.el-select__wrapper) {
  min-height: 40px;
  padding: 4px 8px;
}
</style>