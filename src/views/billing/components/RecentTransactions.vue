<template>
  <div class="mb-8">
    <el-card class="transactions-card" shadow="hover">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-semibold text-gray-800">最近交易记录</span>
          <div class="flex items-center gap-2"></div>
        </div>
      </template>

      <div class="transactions-content">
        <el-table
          :data="filteredTransactions"
          style="width: 100%"
          stripe
          :row-style="rowStyle"
          :header-cell-style="{
            backgroundColor: '#f9fafb',
            color: '#374151',
            fontWeight: '600',
            borderBottom: '1px solid #e5e7eb',
            textAlign: 'center',
          }"
          :cell-style="{ textAlign: 'center' }"
        >
          <el-table-column prop="date" label="交易日期" min-width="90" sortable>
            <template #default="{ row }">
              <div class="flex items-center justify-center">
                <Calendar class="w-4 h-4 text-gray-400 mr-2" />
                {{ row.date }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="customerName" label="客户姓名" min-width="120">
            <template #default="{ row }">
              <div class="flex items-center justify-center">
                <div class="w-8 h-8 rounded-full bg-pink-100 flex items-center justify-center mr-3">
                  <span class="text-sm font-medium text-pink-600">
                    {{ row.customerName.charAt(0) }}
                  </span>
                </div>
                <span class="font-medium text-gray-800">{{ row.customerName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="packageType" label="套餐类型" min-width="140">
            <template #default="{ row }">
              <el-tag :type="getPackageTagType(row.packageType)" size="small" round>
                {{ row.packageType }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="roomNumber" label="房间号" min-width="140">
            <template #default="{ row }">
              <el-tag :type="getPackageTagType(row.roomNumber)" size="small" round>
                {{ row.roomNumber }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="amount" label="金额" min-width="120" sortable>
            <template #default="{ row }">
              <span class="font-semibold text-lg text-gray-800">{{ row.amount }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="paymentMethod" label="支付方式" min-width="160">
            <template #default="{ row }">
              <div class="payment-methods-display">
                <el-tag
                  v-for="method in row.paymentMethods"
                  :key="method"
                  size="small"
                  class="payment-method-tag"
                >
                  <component
                    :is="getPaymentIcon(method)"
                    class="w-3 h-3 mr-1"
                  />
                  {{ method }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" min-width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small" round effect="light">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- <el-table-column label="操作" min-width="120" fixed="right">
            <template #default="{ row }">
              <div class="flex items-center gap-1 justify-center">
                <el-tooltip content="查看详情" placement="top">
                  <el-button size="small" text @click="viewTransactionDetail(row)">
                    <View class="w-4 h-4" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="打印票据" placement="top">
                  <el-button size="small" text @click="printReceipt(row)">
                    <Printer class="w-4 h-4" />
                  </el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column> -->
        </el-table>

        <!-- 分页已隐藏，只显示最近7条记录 -->
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  Search,
  Download,
  Calendar,
  View,
  Printer,
  CircleCheck,
  Clock,
  CreditCard,
  Iphone,
  OfficeBuilding,
  Money,
  User,
  TrendCharts,
  Bottom,
  Warning,
} from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)

const transactions = computed(() => {
  if (!props.data.recently_bill) return []

  return props.data.recently_bill.map(item => ({
    id: item.bid,
    date: item.transaction_date,
    customerName: item.maternity_name,
    roomNumber: item.room_number,
    packageType: item.package_name,
    amount: formatCurrency(item.total_amount),
    paymentMethods: getPaymentMethodsArray(item.payment_method),
    status: getPaymentStatusText(item.payment_status),
  }))
})

const formatCurrency = (amount) => {
  return '¥' + amount.toLocaleString()
}

// 获取支付方式数组
const getPaymentMethodsArray = (paymentMethod) => {
  if (!paymentMethod) return []

  // 如果是数组，直接返回处理后的文本数组
  if (Array.isArray(paymentMethod)) {
    return paymentMethod.map(method => getPaymentMethodText(method))
  }

  // 如果是字符串，尝试解析为数组
  if (typeof paymentMethod === 'string') {
    try {
      // 尝试解析JSON格式的字符串
      const parsed = JSON.parse(paymentMethod)
      if (Array.isArray(parsed)) {
        return parsed.map(method => getPaymentMethodText(method))
      } else {
        return [getPaymentMethodText(paymentMethod)]
      }
    } catch {
      // 如果解析失败，当作单个支付方式处理
      return [getPaymentMethodText(paymentMethod)]
    }
  }

  return []
}

const getPaymentMethodText = (method) => {
  const methodMap = {
    'CASH': '现金',
    'WECHAT_PAY': '微信支付',
    'ALIPAY_PAY': '支付宝支付',
    'BANK_CARD': '银行卡',
    'CREDIT_CARD': '信用卡',
    'BANK_TRANSFER': '银行转账',
    'OTHER': '其他'
  }
  return methodMap[method] || method
}

const getPaymentStatusText = (status) => {
  const statusMap = {
    'FULL_PAID': '已完成',
    'PARTIAL_PAID': '部分支付',
    'UNPAID': '待支付',
    'REFUNDED': '已退款',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

const totalTransactions = computed(() => transactions.value.length)

const filteredTransactions = computed(() => {
  if (!searchKeyword.value) {
    return transactions.value.slice(0, 7)
  }
  return transactions.value.filter((transaction) =>
    transaction.customerName.toLowerCase().includes(searchKeyword.value.toLowerCase()),
  ).slice(0, 7)
})

const rowStyle = ({ rowIndex }) => {
  return {
    backgroundColor: rowIndex % 2 === 0 ? '#fff' : '#fafafa',
  }
}

const getPackageTagType = (packageType) => {
  return 'info'
}

const getStatusTagType = (status) => {
  const statusMap = {
    '已完成': 'success',
    '部分支付': 'warning',
    '待支付': 'warning',
    '已退款': 'info',
    '已取消': 'danger',
  }
  return statusMap[status] || 'info'
}

const getPaymentIcon = (paymentMethod) => {
  const iconMap = {
    '银行转账': OfficeBuilding,
    '支付宝支付': Iphone,
    '微信支付': Iphone,
    '现金': Money,
    '银行卡': CreditCard,
    '信用卡': CreditCard,
    '其他': CreditCard,
  }
  return iconMap[paymentMethod] || CreditCard
}

const viewTransactionDetail = (row) => {
  console.log('查看交易详情:', row)
}

const printReceipt = (row) => {
  console.log('打印票据:', row)
}

</script>

<style scoped>
.transactions-card {
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.transactions-card:hover {
  border-color: rgba(231, 127, 161, 0.3);
}

.transactions-content {
  padding: 0;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table__row) {
  transition: all 0.2s ease;
}

:deep(.el-table__row:hover > td) {
  background-color: rgba(231, 127, 161, 0.05) !important;
}

:deep(.el-pagination) {
  justify-content: center;
}
</style>
