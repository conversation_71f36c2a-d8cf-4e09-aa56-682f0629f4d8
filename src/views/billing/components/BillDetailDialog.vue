<template>
  <el-dialog
    v-model="visible"
    title="结算单详情"
    width="900px"
    align-center
    :before-close="handleClose"
    class="bill-detail-dialog"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 套餐信息 -->
        <div class="detail-section">
          <h3 class="section-title">套餐信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>套餐名称：</label>
              <span class="font-semibold">{{ detailData.package.name }}</span>
            </div>
            <div class="detail-item">
              <label>套餐状态：</label>
              <el-tag :type="detailData.package.status === 'ACTIVE' ? 'success' : 'danger'">
                {{ detailData.package.status === 'ACTIVE' ? '激活' : '未激活' }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>套餐价格：</label>
              <span class="font-medium text-red-600">¥{{ parseFloat(detailData.package.price).toLocaleString() }}</span>
            </div>
            <div class="detail-item">
              <label>套餐天数：</label>
              <span>{{ detailData.package.stay_days }} 天</span>
            </div>
            <div class="detail-item col-span-2">
              <label>套餐描述：</label>
              <span>{{ detailData.package.description }}</span>
            </div>
          </div>
        </div>

        <!-- 入院记录信息 -->
        <div class="detail-section">
          <h3 class="section-title">入院记录信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>产妇姓名：</label>
              <span class="font-semibold">{{ detailData.maternity_admission.maternity_name }}</span>
            </div>
            <div class="detail-item">
              <label>房间号：</label>
              <span class="font-medium">{{ detailData.maternity_admission.room.room_number }}</span>
            </div>
            <div class="detail-item">
              <label>房间类型：</label>
              <span>{{ detailData.maternity_admission.room.room_type }}</span>
            </div>
            <div class="detail-item">
              <label>入住日期：</label>
              <span>{{ detailData.maternity_admission.check_in_date }}</span>
            </div>
            <div class="detail-item">
              <label>出院日期：</label>
              <span>{{ detailData.maternity_admission.check_out_date }}</span>
            </div>
          </div>
        </div>

        <!-- 续住费用信息 -->
        <div v-if="detailData.ma_renew && detailData.ma_renew.length > 0" class="detail-section">
          <h3 class="section-title">续住费用信息</h3>
          <div class="renew-list space-y-3">
            <div
              v-for="(renew, index) in detailData.ma_renew"
              :key="index"
              class="renew-item p-4 bg-gray-50 rounded-lg border"
            >
              <div class="grid grid-cols-2 gap-4">
                <div class="detail-item">
                  <label>续住单号：</label>
                  <span class="font-mono text-sm">{{ renew.rbid }}</span>
                </div>
                <div class="detail-item">
                  <label>续住天数：</label>
                  <span class="font-medium">{{ renew.renew_days }} 天</span>
                </div>
                <div class="detail-item">
                  <label>续住费用：</label>
                  <span class="font-medium text-red-600">¥{{ parseFloat(renew.renew_cost).toLocaleString() }}</span>
                </div>
                <div class="detail-item">
                  <label>费用备注：</label>
                  <span>{{ renew.renew_cost_remark || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 费用明细 -->
        <div class="detail-section">
          <h3 class="section-title">费用明细</h3>
          <div class="cost-summary p-4 bg-blue-50 rounded-lg">
            <div class="grid grid-cols-2 gap-4">
              <div class="detail-item">
                <label>套餐价格：</label>
                <span class="font-medium">¥{{ parseFloat(detailData.package_price).toLocaleString() }}</span>
              </div>
              <div class="detail-item">
                <label>押金金额：</label>
                <span class="font-medium">¥{{ parseFloat(detailData.deposit_amount).toLocaleString() }}</span>
              </div>
              <div class="detail-item">
                <label>定金金额：</label>
                <span class="font-medium">¥{{ parseFloat(detailData.earnest_amount).toLocaleString() }}</span>
              </div>
              <div class="detail-item">
                <label>应付金额：</label>
                <span class="font-medium text-blue-600">¥{{ parseFloat(detailData.payable_amount).toLocaleString() }}</span>
              </div>
              <div class="detail-item">
                <label>已付金额：</label>
                <span class="font-medium text-green-600">¥{{ parseFloat(detailData.paid_amount).toLocaleString() }}</span>
              </div>
              <div class="detail-item">
                <label>剩余尾款：</label>
                <span 
                  class="font-medium"
                  :class="parseFloat(detailData.remaining_amount) < 0 ? 'text-green-600' : 'text-red-600'"
                >
                  ¥{{ parseFloat(detailData.remaining_amount).toLocaleString() }}
                  <span v-if="parseFloat(detailData.remaining_amount) < 0" class="text-sm text-gray-500 ml-1">(已超付)</span>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 支付信息 -->
        <div class="detail-section">
          <h3 class="section-title">支付信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>支付方式：</label>
              <span>{{ getPaymentMethodText(detailData.payment_method) }}</span>
            </div>
            <div class="detail-item">
              <label>支付状态：</label>
              <el-tag 
                :type="getPaymentStatusType(detailData.payment_status)"
                :effect="detailData.payment_status === 'FULL_PAID' || detailData.payment_status === 'REFUNDED' ? 'dark' : 'plain'"
                size="small"
              >
                {{ getPaymentStatusText(detailData.payment_status) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>结算单号：</label>
              <span class="font-mono text-sm">{{ detailData.bid }}</span>
            </div>
            <div class="detail-item col-span-1">
              <label>备注：</label>
              <span>{{ detailData.remark || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 时间信息 -->
        <div class="detail-section">
          <h3 class="section-title">时间信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ detailData.created_at }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ detailData.updated_at }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          type="primary" 
          @click="handleEdit"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          编辑结算单
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElDialog, ElTag, ElButton, ElMessage } from 'element-plus'
import { get } from '@/utils/request'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  billId: {
    type: String,
    default: ''
  }
})

// 定义事件
const emit = defineEmits(['update:visible', 'edit'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听billId变化，获取详情数据
watch(
  () => props.billId,
  async (newBillId) => {
    if (newBillId && props.visible) {
      await fetchBillDetail(newBillId)
    }
  },
  { immediate: true }
)

// 监听弹窗显示状态
watch(
  () => props.visible,
  async (newVisible) => {
    if (newVisible && props.billId) {
      await fetchBillDetail(props.billId)
    }
  }
)

// 获取结算单详情
const fetchBillDetail = async (billId) => {
  loading.value = true
  try {
    const data = await get(`/organizational-management/bills/detail/${billId}/`)
    detailData.value = data
  } catch (error) {
    console.error('获取结算单详情失败:', error)
    ElMessage.error('获取结算单详情失败')
  } finally {
    loading.value = false
  }
}

// 获取支付方式文本
const getPaymentMethodText = (method) => {
  const methodMap = {
    CASH: '现金',
    BANK_CARD: '银行卡',
    CREDIT_CARD: '信用卡',
    WECHAT_PAY: '微信支付',
    ALIPAY_PAY: '支付宝支付',
    OTHER: '其他'
  }
  return methodMap[method] || method
}

// 获取支付状态文本
const getPaymentStatusText = (status) => {
  const statusMap = {
    FULL_PAID: '已全款支付',
    PARTIAL_PAID: '部分支付',
    UNPAID: '未支付',
    REFUNDED: '已退款'
  }
  return statusMap[status] || status
}

// 获取支付状态标签类型
const getPaymentStatusType = (status) => {
  const typeMap = {
    FULL_PAID: 'success',      // 已全款支付 - 绿色
    PARTIAL_PAID: 'warning',   // 已部分支付 - 橙色
    UNPAID: 'danger',          // 未支付 - 红色
    REFUNDED: 'danger',        // 已退款 - 红色（更显眼）
  }
  
  return typeMap[status] || 'info'
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  detailData.value = null
}

// 编辑结算单
const handleEdit = () => {
  emit('edit', detailData.value)
}
</script>

<style scoped>
.detail-section {
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: start;
  gap: 0.5rem;
}

.detail-item label {
  font-weight: 500;
  color: #6b7280;
  white-space: nowrap;
  min-width: 80px;
}

.detail-item span {
  color: #1f2937;
  word-break: break-all;
}

.cost-summary {
  border: 1px solid #93c5fd;
}

.renew-item {
  border: 1px solid #d1d5db;
}

.renew-item:hover {
  border-color: #ec4899;
  background-color: #fdf2f8;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 确保支付状态标签样式与表格一致 */
:deep(.el-tag) {
  font-weight: normal;
}

/* 已退款 - 红色实心 */
:deep(.el-tag.el-tag--danger.el-tag--dark) {
  color: #fff !important;
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
}

/* 已全款支付 - 绿色实心 */
:deep(.el-tag.el-tag--success.el-tag--dark) {
  color: #fff !important;
  background-color: #2BEA8E !important;
  border-color: #2BEA8E !important;
}

/* 部分支付 - 橙色边框 */
:deep(.el-tag.el-tag--warning.el-tag--plain) {
  color: #e6a23c !important;
  background-color: #fdf6ec !important;
  border-color: #f5dab1 !important;
}

/* 未支付 - 红色边框 */
:deep(.el-tag.el-tag--danger.el-tag--plain) {
  color: #f56c6c !important;
  background-color: #fef0f0 !important;
  border-color: #fbc4c4 !important;
}
</style>