<template>
  <div class="handover-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">值班与交接班管理 - 电子交接班</h1>
            <p class="text-sm text-gray-600 mt-1">进行班次交接和信息传递，记录重要事项</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleExportPDF"
            class="bg-pink-400 hover:bg-pink-500 border-pink-400"
          >
            <el-icon class="mr-2">
              <Download />
            </el-icon>
            导出交接日志
          </el-button>
          <el-button
            type="primary"
            @click="handleCreateHandover"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            发起新交班
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 交接班列表组件 -->
    <HandoverTable
      :handovers="handovers"
      :loading="loading"
      @view="handleViewHandover"
      @confirm="handleConfirmHandover"
      @communication="handleCommunication"
    />

    <!-- 交接班表单弹窗 -->
    <HandoverFormDialog
      v-model:visible="formVisible"
      :handover-data="currentHandover"
      :mode="formMode"
      @save="handleSaveHandover"
    />

    <!-- 确认交接弹窗 -->
    <HandoverConfirmDialog
      v-model:visible="confirmVisible"
      :handover-data="currentHandover"
      @save="handleSaveConfirm"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElButton, ElMessage, ElIcon } from 'element-plus'
import { Plus, Download } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import HandoverTable from './components/HandoverTable.vue'
import HandoverFormDialog from './components/HandoverFormDialog.vue'
import HandoverConfirmDialog from './components/HandoverConfirmDialog.vue'

// 响应式数据
const loading = ref(false)
const formVisible = ref(false)
const confirmVisible = ref(false)
const formMode = ref('add') // 'add' | 'view'
const currentHandover = ref(null)

// 筛选条件
const filters = reactive({
  department: '',
  date: '',
  giver: '',
  receiver: '',
  status: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'department',
    type: 'select',
    label: '交班部门',
    placeholder: '选择部门',
    options: [
      { label: '护理部', value: '护理部' },
      { label: '产康部', value: '产康部' },
      { label: '营养部', value: '营养部' },
      { label: '客服部', value: '客服部' },
    ],
  },
  {
    key: 'date',
    type: 'date',
    label: '交班日期',
    placeholder: '选择日期',
  },
  {
    key: 'giver',
    type: 'input',
    label: '交班人',
    placeholder: '搜索交班人',
  },
  {
    key: 'receiver',
    type: 'input',
    label: '接班人',
    placeholder: '搜索接班人',
  },
  {
    key: 'status',
    type: 'select',
    label: '状态',
    placeholder: '选择状态',
    options: [
      { label: '待确认', value: 'pending' },
      { label: '已确认', value: 'confirmed' },
      { label: '有疑问', value: 'questioned' },
    ],
  },
]

// 交接班列表数据
const handovers = ref([
  {
    id: 'HO001',
    department: '护理部',
    giver: '张三丰',
    receiver: '周芷若',
    handoverTime: '2025-01-09 16:05',
    shift: '早班',
    status: 'confirmed',
    statusText: '已确认',
    summary: '本班次共护理产妇15人，新生儿16人，无异常情况',
    keyPoints: '301房产妇情绪波动，需重点关注',
    unfinished: '302房产妇明日出院手续需办理',
  },
  {
    id: 'HO002',
    department: '产康部',
    giver: '李清照',
    receiver: '王语嫣',
    handoverTime: '2025-01-09 08:10',
    shift: '夜班',
    status: 'pending',
    statusText: '待确认',
    summary: '夜班期间为8位产妇进行产后康复治疗',
    keyPoints: '2楼治疗仪故障，已报修',
    unfinished: '205房产妇明日需继续骨盆修复治疗',
  },
  {
    id: 'HO003',
    department: '护理部',
    giver: '赵敏',
    receiver: '小龙女',
    handoverTime: '2025-01-08 23:55',
    shift: '晚班',
    status: 'questioned',
    statusText: '有疑问',
    summary: '晚班护理工作正常进行，无特殊情况',
    keyPoints: '药品库存需补充，已列清单',
    unfinished: '等待药品采购确认',
  },
])

// 方法
const handleCreateHandover = () => {
  currentHandover.value = null
  formMode.value = 'add'
  formVisible.value = true
}

const handleViewHandover = (handover) => {
  currentHandover.value = { ...handover }
  formMode.value = 'view'
  formVisible.value = true
}

const handleConfirmHandover = (handover) => {
  currentHandover.value = { ...handover }
  confirmVisible.value = true
}

const handleCommunication = (handover) => {
  ElMessage.info('发起沟通功能开发中...')
}

const handleExportPDF = () => {
  ElMessage.success('正在导出PDF...')
  // 这里应该调用导出PDF的API
}

const handleSearch = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    // 这里应该调用实际的API
    loading.value = false
  }, 500)
}

const handleSaveHandover = (handoverData) => {
  if (formMode.value === 'add') {
    // 添加新交接班
    handovers.value.unshift({
      ...handoverData,
      id: 'HO' + String(Date.now()).slice(-3).padStart(3, '0'),
      status: 'pending',
      statusText: '待确认',
    })
    ElMessage.success('交班记录创建成功')
  } else {
    // 更新交接班
    const index = handovers.value.findIndex((item) => item.id === handoverData.id)
    if (index !== -1) {
      handovers.value[index] = handoverData
    }
    ElMessage.success('交班记录更新成功')
  }
  formVisible.value = false
}

const handleSaveConfirm = (confirmData) => {
  const index = handovers.value.findIndex((item) => item.id === confirmData.id)
  if (index !== -1) {
    handovers.value[index] = {
      ...handovers.value[index],
      ...confirmData,
      status: 'confirmed',
      statusText: '已确认',
    }
  }
  confirmVisible.value = false
  ElMessage.success('交接确认成功')
}

onMounted(() => {
  // 初始化数据加载
  handleSearch()
})
</script>

<style scoped>
.handover-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
