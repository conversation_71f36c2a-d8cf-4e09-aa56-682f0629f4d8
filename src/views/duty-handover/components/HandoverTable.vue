<template>
  <div class="handover-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Document />
          </el-icon>
          交接班日志列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="handovers"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
    >
      <el-table-column prop="id" label="交班编号" width="100" fixed="left">
        <template #default="{ row }">
          <span class="font-mono font-medium">{{ row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="department" label="交班部门" width="120">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-icon class="mr-2 text-pink-500">
              <House />
            </el-icon>
            <span class="font-medium">{{ row.department }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="giver" label="交班人" width="120">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.giver }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="receiver" label="接班人" width="120">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.receiver }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="handoverTime" label="交班时间" width="160">
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.handoverTime }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="shift" label="班次" width="100">
        <template #default="{ row }">
          <el-tag :type="getShiftTagType(row.shift)" size="small" effect="light">
            {{ row.shift }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="120">
        <template #default="{ row }">
          <el-tag
            :type="getStatusTagType(row.status)"
            :effect="row.status === 'confirmed' ? 'plain' : 'dark'"
            size="small"
          >
            {{ row.statusText }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="summary" label="工作总结" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.summary }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="handleView(row)"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
            >
              查看
            </el-button>

            <el-button
              v-if="row.status === 'pending'"
              type="success"
              size="small"
              @click="handleConfirm(row)"
              class="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600"
            >
              确认交接
            </el-button>

            <el-button
              v-if="row.status === 'questioned'"
              type="warning"
              size="small"
              @click="handleCommunication(row)"
              class="bg-orange-500 hover:bg-orange-600 border-orange-500 hover:border-orange-600"
            >
              发起沟通
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && handovers.length === 0" class="text-center py-16">
      <div class="text-gray-400 text-6xl mb-4">
        <el-icon><Document /></el-icon>
      </div>
      <p class="text-gray-500 text-lg">暂无交接班记录</p>
      <p class="text-gray-400 text-sm mt-2">点击上方"发起新交班"按钮创建第一条记录</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElTable, ElTableColumn, ElTag, ElButton, ElPagination, ElIcon } from 'element-plus'
import { Document, House } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  handovers: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits(['view', 'confirm', 'communication', 'pagination-change'])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = computed(() => props.handovers.length)

// 方法
const handleView = (row) => {
  emit('view', row)
}

const handleConfirm = (row) => {
  emit('confirm', row)
}

const handleCommunication = (row) => {
  emit('communication', row)
}

const getStatusTagType = (status) => {
  const types = {
    pending: 'warning',
    confirmed: 'success',
    questioned: 'danger',
  }
  return types[status] || 'info'
}

const getShiftTagType = (shift) => {
  const types = {
    早班: 'success',
    中班: 'warning',
    晚班: 'info',
    夜班: '',
  }
  return types[shift] || 'info'
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  emit('pagination-change', { page: currentPage.value, size })
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('pagination-change', { page, size: pageSize.value })
}
</script>

<style scoped>
.handover-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.handover-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}
</style>
